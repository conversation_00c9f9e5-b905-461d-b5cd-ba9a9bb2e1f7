{"about.bilibili": "B站", "about.checkUpdate": "检查更新", "about.communication": "交流渠道", "about.copyright": "Copyright © 2023-2025 Kevin2li", "about.friendlyLinks": "友情链接", "about.github": "<PERSON><PERSON><PERSON>", "about.officialWebsite": "软件官网", "about.paidUsersOnly": "仅限付费用户加入，加群请备注\"购买平台+订单编号\"，例如：淘宝xx。", "about.privacyPolicy": "隐私政策", "about.qqChannel": "QQ频道", "about.qqGroup": "QQ群", "about.scanQrCode": "扫描下方二维码加入", "about.termsOfService": "使用条款", "about.title": "关于和帮助", "about.version": "Version", "about.wechatGroup": "微信群", "activate.activate": "激活", "activate.activate_time": "激活时间", "activate.activated": "已激活", "activate.activationCode": "激活码", "activate.activationInfo": "激活信息", "activate.allowed_divices": "允许设备数", "activate.buyActivationCode": "购买激活码?", "activate.deactivate.cancel": "取消", "activate.deactivate.confirm": "确认注销", "activate.deactivate.description": "请注意：\n1. 注销后将释放当前设备的激活名额。 \n2. 注销后使用原激活码无法再次激活该设备，除非重装系统。 \n确定要注销此设备吗？", "activate.deactivate.title": "注销设备", "activate.enterActivationCode": "请输入激活码", "activate.faq": "常见问答", "activate.not_activated": "未激活", "activate.purchase.goToWebsite": "前往官网", "activate.purchase.officialWebsite": "官网购买：", "activate.purchase.onlineStore": "网店购买：", "activate.purchase.pdd": "拼多多", "activate.purchase.taobao": "淘宝", "activate.purchase.title": "购买激活码", "activate.requestTrial": "申请试用", "activate.state": "激活状态", "activate.title": "软件激活", "activate.trialRequest.cancel": "取消", "activate.trialRequest.confirm": "确认", "activate.trialRequest.description": "试用期为3天，期间可以体验所有功能。试用期结束后需要购买激活码继续使用。", "activate.trialRequest.title": "申请试用", "activate.unregister_device": "注销此设备", "anki.card_media_manager.at_least_one_card_type": "至少选择一种媒体类型", "anki.card_media_manager.compressing_images": "正在压缩图片", "anki.card_media_manager.compression_completed": "压缩完成：成功 @success/@total 个文件", "anki.card_media_manager.compression_failed": "压缩图片失败：@error", "anki.card_media_manager.compression_source": "来源", "anki.card_media_manager.compression_tab": "图片压缩", "anki.card_media_manager.copying_file": "正在复制文件：@current/@total - @filename", "anki.card_media_manager.deck": "牌组", "anki.card_media_manager.deck_compression_completed": "牌组压缩完成：成功 @success/@total 个文件", "anki.card_media_manager.deck_compression_completed_with_updates": "牌组压缩完成：压缩 @compressed 个图片，更新 @updated/@total 张卡片", "anki.card_media_manager.deck_compression_failed": "牌组压缩失败：@error", "anki.card_media_manager.description": "支持压缩牌组中图片、自动上传牌组内图片到图床、提取牌组内图片等", "anki.card_media_manager.failed_to_get_media_path": "获取Anki媒体路径失败", "anki.card_media_manager.file": "文件", "anki.card_media_manager.function_description": "功能说明", "anki.card_media_manager.getting_deck_cards": "正在获取牌组卡片", "anki.card_media_manager.include_audio": "包含音频", "anki.card_media_manager.include_network_image": "包含网络图片", "anki.card_media_manager.include_video": "包含视频", "anki.card_media_manager.initializing": "正在初始化", "anki.card_media_manager.invalid_quality": "压缩质量无效，请输入1-100之间的数值", "anki.card_media_manager.invalid_source_mode": "无效的来源模式", "anki.card_media_manager.media_type_local_audio": "本地音频", "anki.card_media_manager.media_type_local_image": "本地图片", "anki.card_media_manager.media_type_local_video": "本地视频", "anki.card_media_manager.media_type_network_audio": "网络音频", "anki.card_media_manager.media_type_network_image": "网络图片", "anki.card_media_manager.media_type_network_video": "网络视频", "anki.card_media_manager.media_types": "媒体类型", "anki.card_media_manager.no_cards_found": "未找到卡片", "anki.card_media_manager.no_deck_selected": "未选择牌组", "anki.card_media_manager.no_files_selected": "未选择文件", "anki.card_media_manager.no_media_found": "未找到媒体文件", "anki.card_media_manager.no_output_dir": "未设置输出目录", "anki.card_media_manager.no_valid_files": "没有有效的文件", "anki.card_media_manager.picgo_address": "Picgo地址", "anki.card_media_manager.picgo_address_cannot_empty": "地址不能为空", "anki.card_media_manager.picgo_address_placeholder": "填写Picgo连接地址", "anki.card_media_manager.quality": "压缩品质", "anki.card_media_manager.quality_error": "请填写正整数", "anki.card_media_manager.quality_placeholder": "设置压缩品质，0~100之间", "anki.card_media_manager.scan_completed": "扫描完成：成功 @success/@total 个文件，失败 @failed 个", "anki.card_media_manager.scan_failed": "扫描图片失败", "anki.card_media_manager.scan_tab": "扫描媒体", "anki.card_media_manager.scanning_cards": "正在扫描卡片：@current/@total", "anki.card_media_manager.scanning_deck": "正在扫描牌组", "anki.card_media_manager.title": "媒体管理", "anki.card_media_manager.updating_note_references": "正在更新笔记引用：@current/@total", "anki.card_media_manager.upload_completed": "上传完成：成功 @success/@total 个文件，失败 @failed 个", "anki.card_media_manager.upload_failed": "上传图片失败：@error", "anki.card_media_manager.upload_failed_no_files_uploaded": "全部文件上传失败", "anki.card_media_manager.upload_tab": "上传图床", "anki.card_media_manager.uploading_file": "正在上传文件：@current/@total - @filename", "anki.common.a_file": "答案文件", "anki.common.advanced_options": "高级选项", "anki.common.answer_cloze_grammar": "答案挖空语法", "anki.common.card_mode": "制卡模式", "anki.common.create_subdeck": "创建子牌组", "anki.common.export_mode": "导出模式", "anki.common.is_answer_cloze": "答案挖空", "anki.common.page_range": "页码范围", "anki.common.q_file": "问题文件", "anki.common.refresh_success": "刷新成功", "anki.common.show_source": "显示来源", "anki.common.tags": "标签", "anki.common.target_deck": "目标牌组", "anki.deck_manager.align_bottom": "底部对齐", "anki.deck_manager.align_center": "居中对齐", "anki.deck_manager.align_top": "顶部对齐", "anki.deck_manager.alignment": "对齐方式", "anki.deck_manager.break_in_line": "行内跨页", "anki.deck_manager.cards_count_invalid": "请输入整数", "anki.deck_manager.cards_count_placeholder": "输入卡片数", "anki.deck_manager.cards_count_required": "请输入卡片数， 例如: 5", "anki.deck_manager.cards_per_file": "单文件卡片数", "anki.deck_manager.cards_per_row": "每行卡片数", "anki.deck_manager.cell_margin": "单元格边距", "anki.deck_manager.cell_margin_invalid": "请输入正确的CSS边距格式，如: 10px 或 5px 10px 15px 20px", "anki.deck_manager.cell_margin_placeholder": "输入边距，如: 10px 或 5px 10px 15px 20px", "anki.deck_manager.cell_margin_required": "请输入边距，如: 10px 或 5px 10px 15px 20px", "anki.deck_manager.cell_padding": "单元格填充", "anki.deck_manager.cell_padding_invalid": "请输入正确的CSS内边距格式，如: 10px 或 5px 10px 15px 20px", "anki.deck_manager.cell_padding_placeholder": "输入内边距，如: 10px 或 5px 10px 15px 20px", "anki.deck_manager.cell_padding_required": "请输入内边距，如: 10px 或 5px 10px 15px 20px", "anki.deck_manager.clone_deck": "克隆牌组", "anki.deck_manager.create_deck": "创建牌组", "anki.deck_manager.create_mode": "创建模式", "anki.deck_manager.create_mode_file": "文件导入", "anki.deck_manager.create_mode_manual": "手动录入", "anki.deck_manager.custom_font_size": "自定义字号", "anki.deck_manager.deck_list": "牌组列表", "anki.deck_manager.deck_list_placeholder": "输入牌组名称, 一行一个牌组", "anki.deck_manager.deck_list_required": "牌组列表不能为空", "anki.deck_manager.deck_names_file": "牌组名称文件", "anki.deck_manager.errors.export_csv_failed": "导出csv失败", "anki.deck_manager.errors.export_html_failed": "导出HTML失败: @error", "anki.deck_manager.errors.export_json_failed": "导出JSON失败", "anki.deck_manager.errors.export_xlsx_failed": "导出Xlsx失败", "anki.deck_manager.errors.select_deck": "请选择牌组", "anki.deck_manager.errors.select_deck_file": "请选择牌组文件", "anki.deck_manager.errors.select_deck_to_remove": "请选择要删除的牌组", "anki.deck_manager.errors.select_output_dir": "请选择输出目录", "anki.deck_manager.errors.select_source_dest_deck": "请选择源牌组和目标牌组", "anki.deck_manager.errors.unsupported_export_mode": "不支持的导出模式： @mode", "anki.deck_manager.export_deck": "导出牌组", "anki.deck_manager.export_format": "导出格式", "anki.deck_manager.export_mode": "导出模式", "anki.deck_manager.export_mode_qa_different_file": "问题答案分开(不同文件)", "anki.deck_manager.export_mode_qa_merge": "问题答案合并", "anki.deck_manager.export_mode_qa_same_page": "问题答案分开(同页并列)", "anki.deck_manager.export_modes.qa_different_file": "问题答案分开(不同文件)", "anki.deck_manager.export_modes.qa_merge": "问题答案合并", "anki.deck_manager.export_modes.qa_same_page": "问题答案分开(同页并列)", "anki.deck_manager.feature_description": "支持批量创建牌组、导入牌组、克隆牌组、导出牌组等", "anki.deck_manager.font_size_invalid": "请输入浮点数", "anki.deck_manager.font_size_placeholder": "输入字号", "anki.deck_manager.font_size_required": "请输入字号， 例如: 12", "anki.deck_manager.function_description": "功能说明", "anki.deck_manager.html.answer_header": "答案", "anki.deck_manager.html.answer_style_comment": "卡片 @cardId 答案样式", "anki.deck_manager.html.answer_title": "答案 @fileIndex", "anki.deck_manager.html.card_style_comment": "卡片 @cardId 样式", "anki.deck_manager.html.card_title": "卡片 @fileIndex", "anki.deck_manager.html.question_header": "问题", "anki.deck_manager.html.question_style_comment": "卡片 @cardId 问题样式", "anki.deck_manager.html.question_title": "问题 @fileIndex", "anki.deck_manager.import_deck": "导入牌组", "anki.deck_manager.include_sched": "包含复习进度", "anki.deck_manager.messages.clone_deck_success": "克隆牌组成功", "anki.deck_manager.messages.create_deck_success": "创建牌组成功", "anki.deck_manager.messages.delete_deck_success": "删除牌组成功", "anki.deck_manager.messages.delete_empty_deck_success": "删除空牌组成功", "anki.deck_manager.messages.export_csv_success": "成功导出 @count 条笔记到csv文件", "anki.deck_manager.messages.export_deck_success": "导出牌组成功", "anki.deck_manager.messages.export_html_success": "导出HTML成功", "anki.deck_manager.messages.export_json_success": "成功导出 @count 条笔记到JSON文件", "anki.deck_manager.messages.export_xlsx_success": "成功导出 @count 条笔记到Xlsx文件", "anki.deck_manager.messages.generating_html": "正在生成HTML文件...", "anki.deck_manager.messages.generating_html_progress": "生成HTML文件 (@current/@total)", "anki.deck_manager.messages.get_media_dir_failed": "获取媒体目录失败", "anki.deck_manager.messages.import_deck_success": "导入牌组成功", "anki.deck_manager.messages.import_from_file_success": "从文件导入牌组成功", "anki.deck_manager.messages.processing": "处理中...", "anki.deck_manager.messages.querying_cards": "正在查询卡片...", "anki.deck_manager.messages.sorting_cards": "正在排序卡片...", "anki.deck_manager.output_directory": "输出目录", "anki.deck_manager.remove_deck": "删除牌组", "anki.deck_manager.remove_deck_label": "删除牌组", "anki.deck_manager.remove_deck_placeholder": "选择删除牌组", "anki.deck_manager.remove_mode": "删除模式", "anki.deck_manager.remove_mode_empty": "删除空牌组", "anki.deck_manager.remove_mode_manual": "手动删除", "anki.deck_manager.remove_range": "删除范围", "anki.deck_manager.remove_range_all": "所有牌组", "anki.deck_manager.remove_range_part": "指定牌组", "anki.deck_manager.sort_direction": "排序方向", "anki.deck_manager.sort_direction_asc": "升序", "anki.deck_manager.sort_direction_desc": "降序", "anki.deck_manager.sort_direction_random": "随机", "anki.deck_manager.sort_method": "排序方式", "anki.deck_manager.sort_method_add_time": "添加时间", "anki.deck_manager.sort_method_due_time": "到期时间", "anki.deck_manager.sort_method_field": "排序字段", "anki.deck_manager.sort_method_lapses": "错题次数", "anki.deck_manager.sort_method_modify_time": "修改时间", "anki.deck_manager.sort_method_reviews": "复习次数", "anki.deck_manager.source_deck": "源牌组", "anki.deck_manager.source_deck_placeholder": "选择源牌组", "anki.deck_manager.source_deck_search_placeholder": "输入源牌组", "anki.deck_manager.target_deck_input_placeholder": "输入目标牌组", "anki.deck_manager.target_deck_label": "目标牌组", "anki.deck_manager.target_deck_placeholder": "输入目标牌组", "anki.deck_manager.target_deck_required": "请输入目标牌组", "anki.deck_manager.target_deck_select_placeholder": "选择目标牌组", "anki.deck_manager.title": "牌组管理", "anki.excel_card.answer_in_column": "答案独占一列", "anki.excel_card.answer_in_text": "答案位于题干中", "anki.excel_card.answer_pattern": "答案匹配模式", "anki.excel_card.answer_position": "答案位置", "anki.excel_card.choice_tab": "选择题", "anki.excel_card.correct_answer_pattern": "正确答案匹配模式", "anki.excel_card.excel_card_title": "Excel制卡", "anki.excel_card.excel_file": "Excel文件", "anki.excel_card.feature_description": "支持Excel问答、选择、判断制卡", "anki.excel_card.field_mapping": "字段映射", "anki.excel_card.free_match": "自由匹配", "anki.excel_card.function_description": "功能说明", "anki.excel_card.guru_import": "<PERSON>导入", "anki.excel_card.id_column": "ID列名", "anki.excel_card.input_answer_pattern": "输入答案匹配模式", "anki.excel_card.input_correct_answer_pattern": "输入正确答案匹配模式", "anki.excel_card.input_option_pattern": "输入选项匹配模式", "anki.excel_card.input_tags": "输入标签", "anki.excel_card.input_template": "输入模版", "anki.excel_card.input_wrong_answer_pattern": "输入错误答案匹配模式", "anki.excel_card.judge_tab": "判断题", "anki.excel_card.no_column_info": "Excel文件中没有列信息", "anki.excel_card.option_in_column": "选项分散各列", "anki.excel_card.option_in_text": "选项只占一列", "anki.excel_card.option_pattern": "选项匹配模式", "anki.excel_card.option_position": "选项位置", "anki.excel_card.qa_tab": "问答题", "anki.excel_card.read_excel_failed": "读取Excel文件失败: @error", "anki.excel_card.select_answer_pattern": "选择答案匹配模式", "anki.excel_card.select_column": "选择列名", "anki.excel_card.select_correct_answer_pattern": "选择正确答案匹配模式", "anki.excel_card.select_excel_file": "选择Excel文件", "anki.excel_card.select_excel_file_error": "请选择Excel文件", "anki.excel_card.select_file_dialog": "选择文件", "anki.excel_card.select_id_column_placeholder": "选择ID列名，作为卡片的唯一ID,可以用来更新卡片", "anki.excel_card.select_option_pattern": "选择选项匹配模式", "anki.excel_card.select_sub_deck_column": "选择子牌组列名", "anki.excel_card.select_tag_column": "选择标签列名", "anki.excel_card.select_tags": "选择标签", "anki.excel_card.select_template": "选择模版", "anki.excel_card.select_worksheet": "选择工作表", "anki.excel_card.select_wrong_answer_pattern": "选择错误答案匹配模式", "anki.excel_card.sub_deck_column": "子牌组列名", "anki.excel_card.table_column": "表格列名", "anki.excel_card.tag_column": "标签列名", "anki.excel_card.template": "模版", "anki.excel_card.template_field": "模版字段", "anki.excel_card.worksheet": "工作表", "anki.excel_card.wrong_answer_pattern": "错误答案匹配模式", "anki.flash.choose_save_location_and_filename": "选择保存位置和文件名", "anki.flash.copy_suffix": "(复制)", "anki.flash.editor.autoSaveFailed": "自动保存失败: @error", "anki.flash.editor.bold": "加粗", "anki.flash.editor.cancel": "取消", "anki.flash.editor.cannotLoadContent": "无法加载原始内容，请重新编辑。", "anki.flash.editor.codeBlock": "代码块", "anki.flash.editor.confirm": "确定", "anki.flash.editor.contentEmpty": "笔记内容为空，删除笔记", "anki.flash.editor.deleteEmptyNoteFailed": "删除空笔记失败: @error", "anki.flash.editor.divider": "添加分隔线", "anki.flash.editor.export": "导出", "anki.flash.editor.exportFailed": "导出失败: @error", "anki.flash.editor.exportFailedCannotSave": "导出失败: 无法保存文件: @error", "anki.flash.editor.exportFailedEmpty": "导出失败: 文档内容为空", "anki.flash.editor.exportFailedEmptyResult": "导出失败: 转换结果为空", "anki.flash.editor.exportSuccess": "导出成功: 文件已保存至: @path", "anki.flash.editor.generatingMindMap": "正在生成思维导图...", "anki.flash.editor.hyperlink": "超链接", "anki.flash.editor.image": "添加图片", "anki.flash.editor.imageDialogTitle": "插入图片", "anki.flash.editor.imageSelectError": "错误: 无法选择图片: @error", "anki.flash.editor.imageSelectLocal": "选择本地图片", "anki.flash.editor.imageUrl": "图片URL", "anki.flash.editor.imageUrlPlaceholder": "https://example.com/image.jpg", "anki.flash.editor.insertImage": "插入图片", "anki.flash.editor.insertLink": "插入链接", "anki.flash.editor.italic": "斜体", "anki.flash.editor.linkCannotOpen": "无法打开链接: @url", "anki.flash.editor.linkDialogTitle": "插入链接", "anki.flash.editor.linkOpenError": "打开链接时出错: @error", "anki.flash.editor.linkUrl": "URL", "anki.flash.editor.linkUrlPlaceholder": "https://example.com", "anki.flash.editor.loadingImage": "正在加载图片...", "anki.flash.editor.mindMapCannotGenerate": "无法生成思维导图，请检查Markdown格式", "anki.flash.editor.mindMapGenerationFailed": "思维导图生成失败", "anki.flash.editor.mindMapMode": "思维导图", "anki.flash.editor.noteTitle": "笔记标题", "anki.flash.editor.noteTitleEmpty": "错误：笔记标题不能为空", "anki.flash.editor.orderedList": "有序列表", "anki.flash.editor.placeholder": "在此编辑笔记内容...", "anki.flash.editor.quote": "引用", "anki.flash.editor.redo": "恢复", "anki.flash.editor.returnToEditor": "返回编辑器", "anki.flash.editor.save": "保存", "anki.flash.editor.saveDialogTitle": "保存Markdown文件", "anki.flash.editor.saveFailed": "保存失败: @error", "anki.flash.editor.saveSuccess": "笔记已保存", "anki.flash.editor.strikethrough": "删除线", "anki.flash.editor.underline": "下划线", "anki.flash.editor.undo": "撤销", "anki.flash.editor.unorderedList": "无序列表", "anki.flash.editor.untitledNote": "未命名笔记", "anki.flash.errors.copy_note_index_out_of_bounds": "复制笔记失败：索引越界", "anki.flash.errors.delete_note_index_out_of_bounds": "删除笔记失败：索引越界", "anki.flash.errors.export_note_index_out_of_bounds": "导出笔记失败：索引越界", "anki.flash.errors.export_to_anki_failed": "导出笔记到Anki失败", "anki.flash.errors.export_to_markdown_failed": "导出笔记到Markdown失败", "anki.flash.errors.load_notes_failed": "加载闪念笔记失败", "anki.flash.errors.save_notes_failed": "保存闪念笔记失败", "anki.flash.errors.update_note_index_out_of_bounds": "更新笔记失败：索引越界", "anki.flash.example_note": "示例笔记", "anki.flash.example_note_content": "这是一条示例笔记，你可以点击编辑或创建新的笔记。\n\n闪念笔记可以用来快速记录想法和灵感。", "anki.flash.example_note_preview": "这是一条示例笔记，你可以点击编辑或创建新的笔记。", "anki.flash.export_failed": "导出失败: @error", "anki.flash.export_feature_under_development": "导出功能开发中...", "anki.flash.messages.exported_to_anki": "导出笔记到Anki", "anki.flash.messages.exported_to_markdown": "导出笔记到Markdown", "anki.flash.messages.loaded_notes": "加载了 @count 条闪念笔记", "anki.flash.messages.saved_notes": "保存了 @count 条闪念笔记", "anki.flash.messages.user_cancelled_export": "用户取消了导出操作", "anki.flash.new_note": "新建笔记", "anki.flash.note_exported_to": "笔记已导出到: @path", "anki.flash.untitled_note": "无标题笔记", "anki.image_card.auto_ocr": "自动OCR", "anki.image_card.cancel": "取消", "anki.image_card.cards_added_to_anki": "@count张卡片已添加到Anki", "anki.image_card.click_or_drag_to_select": "点击选择图片，或拖拽图片到此处", "anki.image_card.click_to_select": "点击选择图片", "anki.image_card.complete_and_return": "完成并返回", "anki.image_card.config_title": "配置", "anki.image_card.deck_name_required": "牌组名称不能为空", "anki.image_card.default_cloze_mode": "默认挖空模式", "anki.image_card.default_cloze_mode_placeholder": "选择默认挖空模式", "anki.image_card.default_deck": "默认牌组", "anki.image_card.default_deck_placeholder": "输入默认牌组名称", "anki.image_card.default_tags": "默认标签", "anki.image_card.default_tags_placeholder": "输入默认标签，用英文逗号分隔", "anki.image_card.delete": "删除", "anki.image_card.delete_all_confirmation": "是否删除所有图片？", "anki.image_card.delete_all_masks": "删除所有遮罩", "anki.image_card.delete_all_masks_confirmation": "确定要删除所有遮罩吗？", "anki.image_card.delete_all_masks_title": "删除所有遮罩", "anki.image_card.delete_image_title": "删除图片", "anki.image_card.failed": "失败", "anki.image_card.failed_to_read_image_from_clipboard": "从剪贴板读取图片失败", "anki.image_card.file_format_error": "请选择符合.png, .jpg, .jpeg, .gif, .bmp格式的文件", "anki.image_card.file_selection_failed": "选择文件失败", "anki.image_card.free_guess": "自由猜", "anki.image_card.group_mode_title": "分组模式", "anki.image_card.guru_import": "<PERSON>导入", "anki.image_card.image_added_from_capture": "已从截图中加载图片", "anki.image_card.image_added_from_clipboard": "已从剪贴板添加图片", "anki.image_card.image_cloze": "图片挖空", "anki.image_card.images_selected": "已选择 @count 张图片", "anki.image_card.mask_all_guess_all": "遮全猜全", "anki.image_card.mask_all_guess_one": "遮全猜一", "anki.image_card.mask_one_guess_one": "遮一猜一", "anki.image_card.masks_deleted_count": "已删除 @count 个遮罩", "anki.image_card.masks_deleted_success": "删除成功", "anki.image_card.next_image": "下一张", "anki.image_card.no_image_selected": "未选择图片", "anki.image_card.no_masks_to_delete": "没有遮罩可删除", "anki.image_card.not_set": "未设置", "anki.image_card.note_label": "笔记", "anki.image_card.note_placeholder": "添加笔记", "anki.image_card.one_cloze_per_card": "一空一卡", "anki.image_card.page_indicator": "第 @current/@total 张", "anki.image_card.paste_hotkey": "贴图快捷键", "anki.image_card.please_add_images_first": "请先添加图片", "anki.image_card.previous_image": "上一张", "anki.image_card.primary_color": "遮罩颜色(主)", "anki.image_card.redo": "恢复", "anki.image_card.scratch_guess": "刮刮乐", "anki.image_card.secondary_color": "遮罩颜色(副)", "anki.image_card.settings_saved": "设置已保存", "anki.image_card.snap_hotkey": "截图快捷键", "anki.image_card.submit": "提交", "anki.image_card.success": "成功", "anki.image_card.tags_label": "标签", "anki.image_card.tags_placeholder": "输入标签，用逗号分隔", "anki.image_card.title": "图片制卡", "anki.image_card.undo": "撤销", "anki.llm_card.add_divider": "添加分隔线", "anki.llm_card.add_image": "添加图片", "anki.llm_card.advanced_settings": "高级设置", "anki.llm_card.ai_card_title": "AI制卡", "anki.llm_card.answer_cloze": "答案挖空", "anki.llm_card.api_address": "API地址", "anki.llm_card.api_key": "API密钥", "anki.llm_card.api_key_cannot_empty": "API密钥不能为空", "anki.llm_card.at_least_one_card_type": "至少选择一种生成题型", "anki.llm_card.at_least_one_difficulty": "至少选择一种题目难度", "anki.llm_card.auto_save_failed": "自动保存失败: @error", "anki.llm_card.bold": "加粗", "anki.llm_card.cancel": "取消", "anki.llm_card.cannot_open_link": "无法打开链接: @url", "anki.llm_card.character_unit": "字", "anki.llm_card.choice_card": "单选", "anki.llm_card.chunk_size": "文本分块大小", "anki.llm_card.chunk_size_cannot_empty": "文本分块大小不能为空", "anki.llm_card.chunk_size_range_error": "文本分块大小必须大于0", "anki.llm_card.cloze_card": "填空", "anki.llm_card.code_block": "代码块", "anki.llm_card.confirm": "确定", "anki.llm_card.confirm_delete": "确认删除", "anki.llm_card.copy_success": "复制成功", "anki.llm_card.copy_suffix": "副本", "anki.llm_card.copy_suffix_numbered": "副本 @number", "anki.llm_card.custom": "自定义", "anki.llm_card.custom_model": "自定义模型", "anki.llm_card.custom_model_required": "使用自定义模型时，请输入模型名称和API端点", "anki.llm_card.default_cloze_mode": "默认填空模式", "anki.llm_card.delete": "删除", "anki.llm_card.delete_prompt_confirmation": "确定要删除提示词\"@name\"吗？", "anki.llm_card.delete_success": "删除成功", "anki.llm_card.difficulty_advanced": "高级", "anki.llm_card.difficulty_basic": "基础", "anki.llm_card.difficulty_medium": "中等", "anki.llm_card.difficulty_none": "无", "anki.llm_card.error": "错误", "anki.llm_card.feature_description": "支持调用AI大模型自动生成卡片", "anki.llm_card.free_guess": "自由猜", "anki.llm_card.function_description": "功能说明", "anki.llm_card.generate_card_type": "生成题型", "anki.llm_card.generate_prompt": "生成提示词", "anki.llm_card.generate_prompt_failed": "生成提示词失败: @error", "anki.llm_card.get_prompt_from_storage": "从存储中获取提示词: @name", "anki.llm_card.guru_import": "<PERSON>导入", "anki.llm_card.guru_official": "<PERSON>官方", "anki.llm_card.html_escape": "HTML转义", "anki.llm_card.hyperlink": "超链接", "anki.llm_card.image_url": "图片URL", "anki.llm_card.input_api_address_placeholder": "输入API地址URL，如： https://example.com/v1", "anki.llm_card.input_api_key_placeholder": "请输入API密钥", "anki.llm_card.input_chunk_size_placeholder": "输入文本分块大小", "anki.llm_card.input_custom_model_name": "输入自定义模型名称", "anki.llm_card.input_max_concurrent_requests_placeholder": "输入最大并发请求数", "anki.llm_card.input_max_tokens_placeholder": "输入最大输出token数", "anki.llm_card.input_model": "输入模型", "anki.llm_card.input_page_range_placeholder": "输入页码范围, 留空表示全部", "anki.llm_card.input_prompt_title_placeholder": "输入提示词标题, 例如: 问答卡片提示词", "anki.llm_card.input_tags": "输入标签", "anki.llm_card.input_temperature_placeholder": "输入温度, 0-2之间", "anki.llm_card.input_timeout_placeholder": "输入超时", "anki.llm_card.input_top_k_placeholder": "输入Top K", "anki.llm_card.input_top_p_placeholder": "输入Top P", "anki.llm_card.insert_image": "插入图片", "anki.llm_card.insert_link": "插入链接", "anki.llm_card.invalid_number": "请输入有效的数字", "anki.llm_card.italic": "斜体", "anki.llm_card.judge_card": "判断", "anki.llm_card.log_directory": "日志目录", "anki.llm_card.mask_all_guess_all": "遮全猜全", "anki.llm_card.mask_all_guess_one": "遮全猜一", "anki.llm_card.mask_one_guess_one": "遮一猜一", "anki.llm_card.max_concurrent_requests": "最大并发请求数", "anki.llm_card.max_concurrent_requests_cannot_empty": "最大并发请求数不能为空", "anki.llm_card.max_concurrent_requests_range_error": "最大并发请求数必须大于0", "anki.llm_card.max_tokens": "最大输出token数", "anki.llm_card.model_provider": "模型提供商", "anki.llm_card.multi_choice_card": "多选", "anki.llm_card.my_prompts": "我的提示词", "anki.llm_card.no_prompts_message": "暂无提示词，请先创建", "anki.llm_card.one_cloze_per_card": "一空一卡", "anki.llm_card.open_link_error": "打开链接时出错: @error", "anki.llm_card.ordered_list": "有序列表", "anki.llm_card.page_range": "页码范围", "anki.llm_card.parse_prompt_json_failed": "解析提示词JSON失败: @error", "anki.llm_card.prompt_copied": "提示词已复制", "anki.llm_card.prompt_deleted": "提示词已删除", "anki.llm_card.prompt_management_tab": "提示词管理", "anki.llm_card.prompt_name_cannot_empty": "提示词名称不能为空", "anki.llm_card.prompt_name_exists_renamed": "提示词名称\"@name\"已存在，自动重命名为\"@uniqueName\"", "anki.llm_card.prompt_saved": "提示词已保存", "anki.llm_card.prompt_selection_changed": "提示词选择变更，重新加载提示词列表: @value", "anki.llm_card.protocol_type": "协议类型", "anki.llm_card.qa_card": "问答", "anki.llm_card.question_difficulty": "题目难度", "anki.llm_card.quote": "引用", "anki.llm_card.reasoning_model": "推理模型", "anki.llm_card.save": "保存", "anki.llm_card.select_at_least_one_card_type": "请至少选择一种卡片类型", "anki.llm_card.select_at_least_one_file": "请选择至少一个文件作为制卡材料", "anki.llm_card.select_local_image": "选择本地图片", "anki.llm_card.select_model": "选择模型", "anki.llm_card.select_model_provider": "选择模型提供商", "anki.llm_card.select_protocol_type": "选择协议类型", "anki.llm_card.select_system_prompt": "选择系统提示词", "anki.llm_card.select_tags": "选择标签", "anki.llm_card.smart_card_tab": "智能制卡", "anki.llm_card.sort_by_create_time": "按创建时间", "anki.llm_card.sort_by_modify_time": "按修改时间", "anki.llm_card.sort_by_name": "按名称", "anki.llm_card.sort_by_word_count": "按字数", "anki.llm_card.strikethrough": "删除线", "anki.llm_card.success": "成功", "anki.llm_card.system_default": "系统默认", "anki.llm_card.system_prompt": "系统提示词", "anki.llm_card.temperature": "温度", "anki.llm_card.temperature_cannot_empty": "温度不能为空", "anki.llm_card.temperature_range_error": "温度必须在0-2之间", "anki.llm_card.test": "测试", "anki.llm_card.timeout_cannot_empty": "超时不能为空", "anki.llm_card.timeout_seconds": "超时(秒)", "anki.llm_card.title": "标题", "anki.llm_card.title_cannot_empty": "标题不能为空", "anki.llm_card.tokens_cannot_empty": "令牌数不能为空", "anki.llm_card.top_k": "Top K", "anki.llm_card.top_k_cannot_empty": "Top K不能为空", "anki.llm_card.top_p": "Top P", "anki.llm_card.top_p_cannot_empty": "Top P不能为空", "anki.llm_card.top_p_range_error": "Top P必须在0-1之间", "anki.llm_card.unable_to_load_content": "无法加载原始内容，请重新编辑。", "anki.llm_card.underline": "下划线", "anki.llm_card.unnamed_prompt": "未命名提示词", "anki.llm_card.unordered_list": "无序列表", "anki.llm_card.update_prompt_name_exists_renamed": "更新的提示词名称\"@name\"已存在，自动重命名为\"@uniqueName\"", "anki.llm_card.use_ai_tags": "使用AI标签", "anki.markdown_card.answer_cloze": "答案挖空", "anki.markdown_card.at_least_one_cloze_grammar": "至少选择一种挖空语法", "anki.markdown_card.chinese_character_set": "中文字符集", "anki.markdown_card.cloze_grammar": "挖空语法", "anki.markdown_card.cloze_tab": "挖空题", "anki.markdown_card.feature_description": "支持将Markdown笔记制卡，支持问答、挖空制卡", "anki.markdown_card.free_guess": "自由猜", "anki.markdown_card.function_description": "功能说明", "anki.markdown_card.guru_import": "<PERSON>导入", "anki.markdown_card.ignore_group": "忽略分组", "anki.markdown_card.input_tags": "输入标签", "anki.markdown_card.mask_all_guess_all": "遮全猜全", "anki.markdown_card.mask_all_guess_one": "遮全猜一", "anki.markdown_card.mask_one_guess_one": "遮一猜一", "anki.markdown_card.media_file_directory": "媒体文件目录", "anki.markdown_card.media_folder_placeholder": "媒体文件夹, 留空则使用文件同级目录", "anki.markdown_card.none": "无", "anki.markdown_card.obsidian_syntax": "Obsidian语法", "anki.markdown_card.one_cloze_per_card": "一空一卡", "anki.markdown_card.please_select_files": "请选择文件!", "anki.markdown_card.please_select_kevin_text_cloze_template": "请选择【Kevin Text Cloze v3】模板", "anki.markdown_card.please_set_front_field_pattern": "请设置Front字段的匹配模式!", "anki.markdown_card.processing_question": "正在处理第 @current 题", "anki.markdown_card.qa_tab": "问答题", "anki.markdown_card.select_tags": "选择标签", "anki.markdown_card.separator": "分隔符", "anki.markdown_card.title": "Markdown制卡", "anki.media_card.audio": "音频", "anki.media_card.card_template": "卡片模版", "anki.media_card.end_time": "结束时间", "anki.media_card.end_time_error": "请输入正确的结束时间, 例如: 00:00:00", "anki.media_card.end_time_placeholder": "输入结束时间, 例如： 00:00:00", "anki.media_card.feature_description": "支持文本、图片、音频、视频等多媒体制卡", "anki.media_card.ffmpeg_processing_failed": "FFmpeg处理失败: @error", "anki.media_card.field_mapping": "字段映射", "anki.media_card.file_content": "文件内容", "anki.media_card.file_suffix": "文件后缀", "anki.media_card.filename": "文件名", "anki.media_card.function_description": "功能说明", "anki.media_card.guru_import": "<PERSON>导入", "anki.media_card.image": "图片", "anki.media_card.input_file_not_exist": "输入文件不存在: @path", "anki.media_card.input_file_suffix": "输入文件后缀", "anki.media_card.input_tags": "输入标签", "anki.media_card.left_padding_error": "请输入正确的左侧填充, 例如: 200", "anki.media_card.left_padding_ms": "左侧填充(毫秒)", "anki.media_card.left_padding_placeholder": "输入左侧填充, 单位: 毫秒", "anki.media_card.match_type": "匹配类型", "anki.media_card.media_card_title": "多媒体制卡", "anki.media_card.media_directory_not_exist": "媒体目录不存在", "anki.media_card.media_file": "媒体文件", "anki.media_card.media_file_directory": "媒体文件目录", "anki.media_card.media_file_directory_placeholder": "请填写媒体文件目录", "anki.media_card.no_matching_files_found": "未找到匹配后缀的文件", "anki.media_card.no_valid_cards_generated": "未生成有效卡片", "anki.media_card.none": "无", "anki.media_card.operation_failed": "操作失败: @error", "anki.media_card.please_configure_ffmpeg_path": "请在首选项中配置ffmpeg路径", "anki.media_card.please_select_file_suffix": "请选择文件后缀", "anki.media_card.please_select_media_directory": "请选择媒体文件目录", "anki.media_card.please_select_subtitle_and_media_files": "请选择字幕文件和媒体文件", "anki.media_card.processing": "处理中...", "anki.media_card.processing_file": "处理文件: @filename", "anki.media_card.qa_card_tab": "问答制卡", "anki.media_card.right_padding_error": "请输入正确的右侧填充, 例如: 200", "anki.media_card.right_padding_ms": "右侧填充(毫秒)", "anki.media_card.right_padding_placeholder": "输入右侧填充, 单位: 毫秒", "anki.media_card.select_card_template": "选择卡片模版", "anki.media_card.select_file_suffix": "选择文件后缀", "anki.media_card.select_match_type": "选择匹配类型", "anki.media_card.select_tags": "选择标签", "anki.media_card.start_time": "开始时间", "anki.media_card.start_time_error": "请输入正确的开始时间, 例如: 00:00:00", "anki.media_card.start_time_placeholder": "输入开始时间, 例如： 00:00:00", "anki.media_card.subtitle_card_tab": "字幕制卡", "anki.media_card.subtitle_file": "字幕文件", "anki.media_card.subtitle_offset_error": "请输入正确的字幕偏移量, 例如: 200", "anki.media_card.subtitle_offset_ms": "字幕偏移量(毫秒)", "anki.media_card.subtitle_offset_placeholder": "输入字幕偏移量, 单位: 毫秒", "anki.media_card.template_field": "模版字段", "anki.media_card.text": "文本", "anki.media_card.video": "视频", "anki.media_card.video_output_format": "视频输出格式", "anki.mindmap.at_least_one_cloze_style": "至少选择一种挖空样式", "anki.mindmap.blue_font": "蓝字", "anki.mindmap.blue_highlight": "蓝色高亮", "anki.mindmap.blue_underline": "蓝色下划线", "anki.mindmap.cloze_mode": "挖空模式", "anki.mindmap.cloze_style": "挖空样式", "anki.mindmap.feature_description": "支持Xmind、知悉、幕布、Markdown等多种来源制卡，不限层级，支持挖空、问答制卡", "anki.mindmap.green_font": "绿字", "anki.mindmap.green_highlight": "绿色高亮", "anki.mindmap.green_underline": "绿色下划线", "anki.mindmap.highlight_color": "高亮颜色", "anki.mindmap.input_color_placeholder": "输入颜色, 如#FF0000", "anki.mindmap.input_file_required": "输入文件不能为空！", "anki.mindmap.map_id_placeholder": "输入MapID, 更新卡片时用到", "anki.mindmap.media_file_directory": "媒体文件目录", "anki.mindmap.media_folder_placeholder": "媒体文件夹, 留空则使用文件同级目录", "anki.mindmap.mindmap_card_title": "导图制卡", "anki.mindmap.mindmap_source": "导图来源", "anki.mindmap.obsidian_syntax": "Obsidian语法", "anki.mindmap.red_font": "红字", "anki.mindmap.red_highlight": "红色高亮", "anki.mindmap.red_underline": "红色下划线", "anki.mindmap.select_color": "选择或填写颜色", "anki.mindmap.source_mubu": "幕布", "anki.mindmap.source_zhixi": "知犀", "anki.mindmap.strikeout": "删除线", "anki.mindmap.text_color": "文本颜色", "anki.mindmap.text_highlight_short": "文本高亮", "anki.mindmap.yellow_highlight": "黄色高亮", "anki.mubu.answer_cloze": "答案挖空", "anki.mubu.back": "背面(系统内置)", "anki.mubu.blue": "蓝色(blue)", "anki.mubu.bold": "加粗", "anki.mubu.cannot_extract_token": "无法从Cookie中提取jwt-token，请检查Cookie是否有效", "anki.mubu.card_generation_complete": "卡片生成完成", "anki.mubu.card_template": "卡片模板", "anki.mubu.card_type": "卡片题型", "anki.mubu.cloze": "挖空题", "anki.mubu.cloze_mode": "挖空模式", "anki.mubu.cloze_style": "挖空样式", "anki.mubu.connection_timeout": "连接超时，请检查网络", "anki.mubu.cookie": "<PERSON><PERSON>", "anki.mubu.cookie_cannot_be_empty": "Cookie不能为空", "anki.mubu.cookie_validation_error": "验证过程发生错误", "anki.mubu.cookie_validation_failed": "Cookie验证失败", "anki.mubu.create_time": "创建时间", "anki.mubu.cyan": "蓝绿色(cyan)", "anki.mubu.document_data_empty": "文档数据为空，无法解析结构", "anki.mubu.document_definition_empty": "文档定义为空，无法解析结构", "anki.mubu.document_id": "文档ID", "anki.mubu.document_no_content": "文档没有内容或解析失败", "anki.mubu.export_failed": "导出失败", "anki.mubu.feature_description": "支持将幕布笔记制卡，支持问答、挖空制卡，也支持批量导出幕布笔记", "anki.mubu.free_guess": "自由猜", "anki.mubu.front": "正面(系统内置)", "anki.mubu.front_field_mapping_error": "请设置一个字段为正面内容 (Front)", "anki.mubu.function_description_title": "功能说明", "anki.mubu.green": "绿色(green)", "anki.mubu.grey": "灰色(grey)", "anki.mubu.height": "高", "anki.mubu.hierarchy": "父子节点型", "anki.mubu.importing_cards": "正在导入卡片...", "anki.mubu.input_cookie": "输入<PERSON><PERSON>", "anki.mubu.input_document": "搜索文档", "anki.mubu.input_tags": "输入标签", "anki.mubu.italic": "斜体", "anki.mubu.item_count": "条目数量", "anki.mubu.level_1": "一级标题", "anki.mubu.level_2": "二级标题", "anki.mubu.level_3": "三级标题", "anki.mubu.level_4": "四级标题", "anki.mubu.level_5": "五级标题", "anki.mubu.level_6": "六级标题", "anki.mubu.mask_all_guess_all": "遮全猜全", "anki.mubu.mask_all_guess_one": "遮全猜一", "anki.mubu.mask_one_guess_one": "遮一猜一", "anki.mubu.mindmap": "完整导图型", "anki.mubu.network_error": "网络错误", "anki.mubu.no_auth_info": "未设置身份验证信息，请先调用setCookie()", "anki.mubu.node_desc": "节点-描述型", "anki.mubu.none": "无", "anki.mubu.olive": "橄榄绿(olive)", "anki.mubu.one_cloze_per_card": "一空一卡", "anki.mubu.page_title": "幕布制卡", "anki.mubu.parse_document_definition_json_failed": "解析文档定义JSON失败", "anki.mubu.part_card": "部分制卡", "anki.mubu.pink": "粉色(pink)", "anki.mubu.processing_node": "正在处理节点", "anki.mubu.purple": "紫色(purple)", "anki.mubu.q_node_level": "问题节点层级", "anki.mubu.qa": "问答题", "anki.mubu.receive_timeout": "接收响应超时，请检查网络", "anki.mubu.red": "红色(red)", "anki.mubu.request_canceled": "请求已取消", "anki.mubu.select_card_template": "选择卡片模板", "anki.mubu.select_cloze_style": "至少选择一种挖空样式", "anki.mubu.select_q_node_level": "选择问题节点层级", "anki.mubu.select_tags": "选择标签", "anki.mubu.select_target_document": "选择目标文档", "anki.mubu.select_text_color": "选择颜色", "anki.mubu.select_text_highlight": "选择颜色", "anki.mubu.send_timeout": "发送请求超时，请检查网络", "anki.mubu.sep": "---", "anki.mubu.server_error": "服务器返回错误", "anki.mubu.show_source": "显示来源", "anki.mubu.strikeout": "删除线", "anki.mubu.tab_card_creation": "笔记制卡", "anki.mubu.tab_note_export": "笔记导出", "anki.mubu.target_document": "目标文档", "anki.mubu.text_color": "文本颜色", "anki.mubu.text_highlight": "高亮颜色", "anki.mubu.underline": "下划线", "anki.mubu.unknow_error": "发生未知错误", "anki.mubu.update_time": "更新时间", "anki.mubu.view_source": "查看出处", "anki.mubu.width": "宽", "anki.mubu.yellow": "黄色(yellow)", "anki.ocr.all_text": "所有文字", "anki.ocr.and_other_errors": "以及其他@count个错误", "anki.ocr.cancel": "取消", "anki.ocr.cannot_convert_image_to_png": "无法将图像转换为PNG格式", "anki.ocr.cannot_process_any_ocr_results": "无法处理任何OCR结果", "anki.ocr.cannot_save_any_valid_image_files": "无法保存任何有效的图像文件", "anki.ocr.card_id": "卡片ID", "anki.ocr.card_id_cannot_empty": "卡片ID不能为空", "anki.ocr.card_id_illegal": "卡片ID不存在或不合法，示例：1736773785607,1736773785608", "anki.ocr.card_id_placeholder": "输入卡片ID列表，用逗号分隔，例如：1736080247115,1736080247119", "anki.ocr.card_ocr_description": "对Anki卡片中的图片进行OCR文字识别并更新字段内容", "anki.ocr.card_ocr_tab": "卡片OCR", "anki.ocr.card_ocr_title": "卡片OCR", "anki.ocr.card_template": "卡片模板", "anki.ocr.clickToSelectImage": "点击选择图片", "anki.ocr.clickToSelectOrDragImage": "点击选择图片，或拖拽图片到此处", "anki.ocr.clipboard_read_failed": "从剪贴板读取图片失败", "anki.ocr.copied": "已复制", "anki.ocr.copied_to_clipboard": "已复制到剪贴板", "anki.ocr.copy": "复制", "anki.ocr.custom": "自定义", "anki.ocr.defaultOcrProvider": "默认OCR服务商", "anki.ocr.delete": "删除", "anki.ocr.deleteAllImages": "是否删除所有图片？", "anki.ocr.deleteImage": "删除图片", "anki.ocr.error_processing_single_image": "处理单张图像时出错: @error", "anki.ocr.error_saving_image_files": "保存图像文件时出错: @error", "anki.ocr.export_failed": "导出失败: @error", "anki.ocr.exported_to": "已导出到: @path", "anki.ocr.failed_to_recognize_any_images": "未能成功识别任何图片", "anki.ocr.feature_description": "对图片或Anki卡片中的图片进行OCR文字识别", "anki.ocr.feature_not_supported_on_current_system": "此功能不支持当前系统", "anki.ocr.field_config": "字段配置", "anki.ocr.file_selection_failed": "选择文件失败", "anki.ocr.fill_field": "填充字段", "anki.ocr.imageDeleted": "图片已被删除或不存在", "anki.ocr.imageOcr": "图片OCR", "anki.ocr.image_data_invalid": "图片数据无效: @path", "anki.ocr.image_file_not_exist": "图片文件不存在: @path", "anki.ocr.image_ocr_tab": "图片OCR", "anki.ocr.merge_output": "合并输出", "anki.ocr.noImageSelected": "未选择图片", "anki.ocr.no_valid_image_in_clipboard": "剪贴板中没有有效的图片", "anki.ocr.ocr_engine_initialization_failed": "OCR引擎初始化失败，请尝试重启应用或更新应用版本", "anki.ocr.ocr_model_file_invalid": "OCR模型文件无效: @fileName", "anki.ocr.ocr_processing_failed": "OCR处理失败: @error", "anki.ocr.ocr_recognition_completed": "OCR识别完成", "anki.ocr.ocr_recognition_failed": "OCR识别失败: @error", "anki.ocr.ocr_recognition_partially_completed": "OCR识别部分完成: @success/@total张图片已处理", "anki.ocr.ocr_service_returned_empty_result": "OCR服务返回空结果", "anki.ocr.original_field": "原始字段", "anki.ocr.pageIndicator": "第 @current/@total 张", "anki.ocr.partial_ocr_recognition_failed": "部分图片OCR识别失败，将处理剩余的图片", "anki.ocr.pleaseSelectValidImageFormat": "请选择符合.png, .jpg, .jpeg格式的文件", "anki.ocr.please_add_images_first": "请先添加图片", "anki.ocr.please_select_cards_first": "请先选择卡片", "anki.ocr.processing_note": "正在处理笔记：@noteId", "anki.ocr.recognizing": "正在识别...", "anki.ocr.save_ocr_text": "保存OCR文本", "anki.ocr.selectOcrProvider": "选择OCR服务商", "anki.ocr.select_card_template": "选择卡片模板", "anki.ocr.select_field": "选择字段", "anki.ocr.selectedImages": "已选择 @count 张图片", "anki.ocr.selected_text": "已选择文字", "anki.ocr.settings": "配置", "anki.ocr.submit": "提交", "anki.ocr.table_ocr_col": "OCR识别", "anki.ocr.table_replace_col": "替换内容", "anki.ocr.text_copied_to_clipboard": "文字已复制到剪贴板", "anki.ocr.title": "OCR识别", "anki.pdf_card.a_item_id": "答案条目ID", "anki.pdf_card.a_page_range": "答案文件页码范围", "anki.pdf_card.annot_type_note": "便签", "anki.pdf_card.annot_type_text": "文本框", "anki.pdf_card.annotation_types": "批注类型", "anki.pdf_card.cloze_mode_free_guess": "自由猜", "anki.pdf_card.cloze_mode_mask_all_guess_all": "遮全猜全", "anki.pdf_card.cloze_mode_mask_all_guess_one": "遮全猜一", "anki.pdf_card.cloze_mode_mask_one_guess_one": "遮一猜一", "anki.pdf_card.cloze_mode_scratch_guess": "刮刮乐", "anki.pdf_card.cloze_tab": "挖空卡", "anki.pdf_card.exporting_item": "正在导出条目...", "anki.pdf_card.extra_info": "额外信息", "anki.pdf_card.feature_description": "支持挖空卡、问答卡制作，支持多种制卡模式", "anki.pdf_card.full_page_cloze": "整页挖空", "anki.pdf_card.function_description": "功能说明", "anki.pdf_card.getting_item_info": "获取条目信息...", "anki.pdf_card.guru_import": "<PERSON>导入", "anki.pdf_card.image_occlusion_builtin": "Image Occlusion(Anki内置)", "anki.pdf_card.invalid_answer_item_id": "无效的答案条目ID: @itemId", "anki.pdf_card.invalid_item_id": "无效的条目ID: @itemId", "anki.pdf_card.invalid_question_item_id": "无效的问题条目ID: @itemId", "anki.pdf_card.main_color": "主色", "anki.pdf_card.mask_color": "遮罩颜色", "anki.pdf_card.mask_type": "遮罩类型", "anki.pdf_card.mask_type_highlight": "高亮", "anki.pdf_card.mask_type_square": "矩形", "anki.pdf_card.mask_type_squiggly": "波浪线", "anki.pdf_card.mask_type_strikeout": "删除线", "anki.pdf_card.mask_type_underline": "下划线", "anki.pdf_card.mix_card": "混合制卡", "anki.pdf_card.one_cloze_per_card": "一空一卡", "anki.pdf_card.pdf_columns": "PDF栏数", "anki.pdf_card.please_select_file": "请选择文件", "anki.pdf_card.q_item_id": "问题条目ID", "anki.pdf_card.q_page_range": "问题文件", "anki.pdf_card.qa_mode_dual_file": "问题答案分开", "anki.pdf_card.qa_mode_dual_page": "双页制卡", "anki.pdf_card.qa_mode_note": "笔记制卡", "anki.pdf_card.qa_mode_single_file": "问题答案连续", "anki.pdf_card.qa_mode_single_page": "单页制卡", "anki.pdf_card.qa_tab": "问答卡", "anki.pdf_card.second_color": "副色", "anki.pdf_card.title": "PDF制卡", "anki.pdf_card.zotero_card": "Zotero制卡", "anki.pdf_card.zotero_item_id": "条目ID", "anki.pdf_card.zotero_windows_mac_only": "Zotero制卡功能仅支持Windows、Mac系统", "anki.pdf_note.actions.failed": "失败", "anki.pdf_note.actions.hint": "提示", "anki.pdf_note.actions.in_development": "开发中", "anki.pdf_note.actions.save": "保存", "anki.pdf_note.actions.success": "成功", "anki.pdf_note.config.auto_paste": "自动粘贴", "anki.pdf_note.config.custom_link_format": "自定义链接格式", "anki.pdf_note.config.custom_link_format_placeholder": "输入自定义链接格式, 如: [{{title}}](<{{url}}>)", "anki.pdf_note.config.link_format": "链接格式", "anki.pdf_note.config.link_protocol": "跳转协议", "anki.pdf_note.content.image": "图片", "anki.pdf_note.content.view_original": "[查看原文]", "anki.pdf_note.description.function_title": "功能说明", "anki.pdf_note.description.main": "边看PDF边记笔记, 支持快捷插入PDF页码回链，点击即可直接跳转原文出处，支持Obsidian、Logseq、飞书、语雀、Notion、Word、Onenote等众多笔记软件", "anki.pdf_note.description.zotero_notice": "注意： 此功能需要搭配Zotero使用", "anki.pdf_note.errors.custom_link_format_empty": "自定义链接格式不能为空", "anki.pdf_note.errors.get_item_notes_failed": "获取条目笔记失败", "anki.pdf_note.errors.get_pdf_info_failed": "获取PDF信息失败", "anki.pdf_note.errors.get_selected_annotation_failed": "获取选中注释失败", "anki.pdf_note.errors.image_data_empty": "图片数据为空", "anki.pdf_note.errors.no_bookmarks_in_pdf": "PDF中没有书签", "anki.pdf_note.errors.no_selected_annotation": "没有选中注释", "anki.pdf_note.errors.pdf_path_empty": "PDF路径为空", "anki.pdf_note.errors.text_data_empty": "文本数据为空", "anki.pdf_note.errors.unsupported_link_format": "不支持的链接格式", "anki.pdf_note.formats.custom": "自定义", "anki.pdf_note.formats.html": "HTML", "anki.pdf_note.formats.markdown": "<PERSON><PERSON>", "anki.pdf_note.formats.url": "Url", "anki.pdf_note.notifications.all_notes_copied_success": "已复制所有笔记", "anki.pdf_note.notifications.all_notes_copy_failed": "插入所有笔记失败", "anki.pdf_note.notifications.annotation_copied_success": "已复制注释", "anki.pdf_note.notifications.annotation_copy_failed": "插入注释失败", "anki.pdf_note.notifications.bookmark_links_copied_success": "已复制书签链接", "anki.pdf_note.notifications.bookmark_links_copy_failed": "插入书签链接失败", "anki.pdf_note.notifications.flag_a_set_failed": "设置标记点A失败", "anki.pdf_note.notifications.flag_a_set_success": "设置标记点A成功", "anki.pdf_note.notifications.flag_b_set_failed": "设置标记点B失败", "anki.pdf_note.notifications.flag_b_set_success": "设置标记点B成功", "anki.pdf_note.notifications.flag_cleared_success": "清除标记点成功", "anki.pdf_note.notifications.flag_jump_failed": "标记点跳转失败", "anki.pdf_note.notifications.go_end_failed": "跳转到末页失败", "anki.pdf_note.notifications.go_home_failed": "跳转到首页失败", "anki.pdf_note.notifications.go_next_failed": "跳转到下一页失败", "anki.pdf_note.notifications.go_prev_failed": "跳转到上一页失败", "anki.pdf_note.notifications.link_copied_success": "已复制链接", "anki.pdf_note.notifications.link_copy_failed": "插入链接失败", "anki.pdf_note.notifications.note_copied_success": "已复制笔记", "anki.pdf_note.notifications.note_copy_failed": "插入笔记失败", "anki.pdf_note.notifications.ocr_failed": "OCR失败", "anki.pdf_note.notifications.ocr_in_development": "OCR功能开发中", "anki.pdf_note.notifications.page_screenshot_failed": "插入页面截图失败", "anki.pdf_note.notifications.page_screenshot_in_development": "页面截图功能开发中", "anki.pdf_note.notifications.page_text_extract_failed": "提取页面文本失败", "anki.pdf_note.notifications.page_text_extract_in_development": "页面文本提取功能开发中", "anki.pdf_note.notifications.please_set_flag_first": "请先设置标记点", "anki.pdf_note.notifications.scroll_down_failed": "向下滚动失败", "anki.pdf_note.notifications.scroll_up_failed": "向上滚动失败", "anki.pdf_note.notifications.settings_saved_success": "保存成功", "anki.pdf_note.notifications.shortcuts_disabled": "已禁用全部快捷键", "anki.pdf_note.notifications.shortcuts_enabled": "已启用全部快捷键", "anki.pdf_note.shortcuts.clear_flag": "清除标记点", "anki.pdf_note.shortcuts.disable_all": "全部禁用", "anki.pdf_note.shortcuts.go_end": "跳转到末页", "anki.pdf_note.shortcuts.go_home": "跳转到首页", "anki.pdf_note.shortcuts.go_next": "下一页", "anki.pdf_note.shortcuts.go_prev": "上一页", "anki.pdf_note.shortcuts.insert_all_notes": "插入所有笔记", "anki.pdf_note.shortcuts.insert_bookmarks_link": "插入书签回链", "anki.pdf_note.shortcuts.insert_comment": "插入注释", "anki.pdf_note.shortcuts.insert_comment_link": "插入注释回链", "anki.pdf_note.shortcuts.insert_note": "插入笔记", "anki.pdf_note.shortcuts.insert_page_link": "插入页码回链", "anki.pdf_note.shortcuts.insert_path_link": "插入路径回链", "anki.pdf_note.shortcuts.jump_to_flag": "跳转到标记点", "anki.pdf_note.shortcuts.not_set": "未设置", "anki.pdf_note.shortcuts.set_flag_a": "设置标记点A", "anki.pdf_note.shortcuts.set_flag_b": "设置标记点B", "anki.pdf_note.tabs.config": "配置", "anki.pdf_note.tabs.shortcuts": "快捷键", "anki.pdf_note.title": "PDF笔记", "anki.placeholder.a_item_id": "输入答案条目ID", "anki.placeholder.a_item_id_required": "答案条目ID不能为空", "anki.placeholder.a_page_range": "答案文件页码范围", "anki.placeholder.atLeastOneAnnotationType": "至少选择一种批注类型", "anki.placeholder.atLeastOneAnswerClozeGrammar": "至少选择一种挖空语法", "anki.placeholder.atLeastOneMaskType": "至少选择一种遮罩类型", "anki.placeholder.extra_info": "请输入额外信息，如《xx书名》", "anki.placeholder.input_item_id": "输入条目ID", "anki.placeholder.input_tags": "输入标签", "anki.placeholder.item_id_required": "条目ID不能为空", "anki.placeholder.mustBeGreaterThan0": "必须大于0", "anki.placeholder.mustBeInteger": "请输入整数", "anki.placeholder.page_range": "输入页码范围, 留空表示全部", "anki.placeholder.page_range_required": "请输入正确的页码范围, 例如: 1-3,5-7", "anki.placeholder.pdf_columns": "请输入PDF栏数", "anki.placeholder.pdf_columns_required": "PDF栏数不能为空", "anki.placeholder.q_item_id": "输入问题条目ID", "anki.placeholder.q_item_id_required": "问题条目ID不能为空", "anki.placeholder.q_page_range": "问题文件页码范围", "anki.placeholder.select_a_file": "请选择答案文件", "anki.placeholder.select_file": "选择要压缩的图片文件", "anki.placeholder.select_q_file": "请选择问题文件", "anki.placeholder.select_tags": "选择标签", "anki.placeholder.tags": "请选择或输入标签", "anki.placeholder.target_deck_search_input": "输入牌组", "anki.placeholder.zotero_item_id": "输入条目ID", "anki.placeholder.zotero_item_id_required": "条目ID不能为空", "anki.sep": "分隔符", "anki.sync.config.dataLocation": "数据存储位置", "anki.sync.config.dataLocationPlaceholder": "请选择数据存储位置", "anki.sync.config.host": "监听地址", "anki.sync.config.hostInvalid": "请输入合法的IP地址或域名", "anki.sync.config.hostPlaceholder": "请输入监听地址", "anki.sync.config.hostRequired": "监听地址不能为空", "anki.sync.config.max_payloa_invalid": "请输入正整数，例如100", "anki.sync.config.max_payloa_required": "最大同步负载不能为空", "anki.sync.config.max_payload": "最大同步负载(MB)", "anki.sync.config.max_payload_placeholder": "输入最大同步负载，默认为100", "anki.sync.config.password": "密码", "anki.sync.config.passwordMinLength": "密码长度不能小于6位", "anki.sync.config.passwordPlaceholder": "请输入密码", "anki.sync.config.passwordRequired": "密码不能为空", "anki.sync.config.port": "监听端口", "anki.sync.config.portInvalid": "请输入1-65535之间的端口号", "anki.sync.config.portPlaceholder": "请输入监听端口", "anki.sync.config.portRequired": "监听端口不能为空", "anki.sync.config.title": "同步配置", "anki.sync.config.username": "用户名", "anki.sync.config.usernamePlaceholder": "请输入用户名", "anki.sync.config.usernameRequired": "用户名不能为空", "anki.sync.configIncomplete": "请填写完整配置", "anki.sync.copied": "已复制: @address", "anki.sync.logStartServerError": "启动服务器错误", "anki.sync.logStopServerError": "停止服务器错误", "anki.sync.needNotificationPermission": "需要通知权限才能启动同步服务器", "anki.sync.no_user": "暂无用户，请先添加", "anki.sync.serverAddress": "服务器地址:", "anki.sync.serverStarted": "同步服务器已启动!", "anki.sync.serverStopped": "同步服务器已停止", "anki.sync.setDataLocation": "请设置数据存储位置", "anki.sync.settingsSaved": "设置已保存", "anki.sync.startServerError": "启动服务器错误: @error", "anki.sync.startServerFailed": "启动同步服务器失败: @message", "anki.sync.startingServer": "正在启动服务器...", "anki.sync.stopServerError": "停止服务器错误", "anki.sync.stopServerFailed": "停止同步服务器失败", "anki.sync.syncServer": "同步服务器", "anki.sync.title": "<PERSON><PERSON>步", "anki.sync.user.add": "添加", "anki.sync.user.addError": "添加用户失败", "anki.sync.user.addFirst": "添加用户", "anki.sync.user.addSuccess": "用户添加成功", "anki.sync.user.addTitle": "添加新用户", "anki.sync.user.cancel": "取消", "anki.sync.user.delete": "删除", "anki.sync.user.deleteConfirm": "确定要删除用户'@username'吗？", "anki.sync.user.deleteError": "删除用户失败", "anki.sync.user.deleteSuccess": "用户删除成功", "anki.sync.user.deleteTitle": "删除用户", "anki.sync.user.edit": "编辑", "anki.sync.user.editTitle": "编辑用户", "anki.sync.user.noUsers": "暂无配置用户", "anki.sync.user.notFound": "用户未找到", "anki.sync.user.title": "用户管理", "anki.sync.user.update": "更新", "anki.sync.user.updateError": "更新用户失败", "anki.sync.user.updateSuccess": "用户更新成功", "anki.sync.user.usernameExists": "用户名已存在", "anki.text": "文本", "anki.text_cannot_empty": "文本不能为空", "anki.text_card.answer_after_option": "答案位于选项后", "anki.text_card.answer_cloze": "答案挖空", "anki.text_card.answer_file_fields": "答案文件包含字段", "anki.text_card.answer_in_question": "答案位于题干中", "anki.text_card.at_least_one_cloze_grammar": "至少选择一种挖空语法", "anki.text_card.card_template": "卡片模版", "anki.text_card.choice_tab": "选择题", "anki.text_card.cloze_grammar": "挖空语法", "anki.text_card.cloze_tab": "挖空题", "anki.text_card.document_type": "文档类型", "anki.text_card.feature_description": "支持TXT、Markdown等多种格式文件制卡，支持挖空卡、问答卡、选择题、判断题制作，支持多种制卡模式", "anki.text_card.file_path_not_exist": "文件路径不存在", "anki.text_card.fixed_options_true_false": "对||错", "anki.text_card.free_guess": "自由猜", "anki.text_card.function_description": "功能说明", "anki.text_card.ignore_group": "忽略分组", "anki.text_card.input_subdeck_prefix": "输入子牌组前缀", "anki.text_card.input_tags": "输入标签", "anki.text_card.judge_tab": "判断题", "anki.text_card.mask_all_guess_all": "遮全猜全", "anki.text_card.mask_all_guess_one": "遮全猜一", "anki.text_card.mask_one_guess_one": "遮一猜一", "anki.text_card.media_file_directory": "媒体文件目录", "anki.text_card.media_folder_placeholder": "媒体文件夹, 留空则使用文件同级目录", "anki.text_card.none": "无", "anki.text_card.normal_separator": "普通分隔", "anki.text_card.obsidian_syntax": "Obsidian语法", "anki.text_card.one_cloze_per_card": "一空一卡", "anki.text_card.please_select_answer_file": "请选择答案文件", "anki.text_card.please_select_file": "请选择文件", "anki.text_card.please_select_file_not_directory": "请选择文件, 不是目录", "anki.text_card.please_select_files": "请选择文件!", "anki.text_card.please_select_kevin_choice_template": "请选择【Kevin Choice Card v2】模板", "anki.text_card.please_select_kevin_text_cloze_template": "请选择【Kevin Text Cloze v3】模板", "anki.text_card.please_select_question_answer_files": "请选择问题文件和答案文件!", "anki.text_card.please_select_question_file": "请选择问题文件", "anki.text_card.please_set_answers_field_pattern": "请设置Answers字段的匹配模式!", "anki.text_card.please_set_front_field_pattern": "请设置Front字段的匹配模式!", "anki.text_card.please_set_question_field_pattern": "请设置Question字段的匹配模式!", "anki.text_card.processing_question": "正在处理第 @current 题", "anki.text_card.qa_tab": "问答题", "anki.text_card.question_answer_count_mismatch": "牌组 @deck 中的问题数量(@questionCount)与答案数量(@answerCount)不匹配!", "anki.text_card.question_answer_cross_file": "问题答案不同文件", "anki.text_card.question_answer_cross_file_choice": "问题答案不同文件", "anki.text_card.question_answer_same_file": "问题答案同文件", "anki.text_card.question_file_fields": "问题文件包含字段", "anki.text_card.question_single_line": "问题单行", "anki.text_card.regex_separator": "正则分隔", "anki.text_card.restore_params": "恢复参数", "anki.text_card.save_params": "保存参数", "anki.text_card.select_answer_file_fields": "选择答案文件包含字段", "anki.text_card.select_answer_file_fields_required": "请选择答案文件包含字段", "anki.text_card.select_answer_file_placeholder": "请选择答案文件", "anki.text_card.select_card_template": "选择卡片模版", "anki.text_card.select_question_file_fields": "选择问题文件包含字段", "anki.text_card.select_question_file_fields_required": "请选择问题文件包含字段", "anki.text_card.select_subdeck_prefix": "选择子牌组前缀", "anki.text_card.select_tags": "选择标签", "anki.text_card.subdeck_prefix": "子牌组前缀", "anki.text_card.title": "文本制卡", "anki.tts.add_voice": "添加发音", "anki.tts.card_id": "卡片ID", "anki.tts.card_id_cannot_empty": "卡片ID不能为空", "anki.tts.card_id_illegal": "卡片ID不存在或不合法, 示例：1736773785607,1736773785608", "anki.tts.card_id_placeholder": "输入卡片ID列表,用英文逗号分隔,例如: 1736080247115,1736080247119", "anki.tts.card_template": "卡片模版", "anki.tts.card_tts_tab": "卡片朗读", "anki.tts.card_tts_title": "卡片朗读", "anki.tts.failed": "失败", "anki.tts.feature_description": "支持Anki卡片批量添加发音，支持多种发音引擎，支持文本转语音", "anki.tts.feature_not_supported_on_current_system": "此功能不支持当前系统", "anki.tts.fill_field": "填充字段", "anki.tts.function_description": "功能说明", "anki.tts.input_sep": "输入分隔符", "anki.tts.input_text": "输入文本...", "anki.tts.is_use_sep": "启用分隔符", "anki.tts.lang.chinese": "中文", "anki.tts.lang.english_uk": "英语(英国)", "anki.tts.lang.english_us": "英语(美国)", "anki.tts.lang.french": "法语", "anki.tts.lang.german": "德语", "anki.tts.lang.italian": "意大利语", "anki.tts.lang.japanese": "日语", "anki.tts.lang.korean": "韩语", "anki.tts.lang.portuguese": "葡萄牙语", "anki.tts.lang.russian": "俄语", "anki.tts.lang.spanish": "西语", "anki.tts.language": "语言", "anki.tts.original_field": "原始字段", "anki.tts.output_directory_cannot_be_empty": "输出目录不能为空", "anki.tts.pitch": "音调", "anki.tts.please_select_cards_first": "请先选择卡片", "anki.tts.processing": "正在处理...", "anki.tts.processing_note": "正在处理笔记: @noteId", "anki.tts.rate": "语速", "anki.tts.remove_voice": "删除发音", "anki.tts.select_card_template": "选择卡片模版", "anki.tts.select_field": "选择字段", "anki.tts.select_language": "选择语言", "anki.tts.select_sep": "选择分隔符", "anki.tts.select_style": "选择风格", "anki.tts.style": "风格", "anki.tts.text_cannot_be_empty": "文本不能为空", "anki.tts.text_tts_tab": "文本转语音", "anki.tts.voice.cantonese_hongkong": "粤语-香港", "anki.tts.voice.female": "女", "anki.tts.voice.male": "男", "anki.tts.voice.multilingual": "多语言", "anki.tts.voice.northeast_dialect": "东北话", "anki.tts.voice.shaanxi_dialect": "陕西话", "anki.tts.voice_field_config": "发音字段配置", "anki.tts.voice_generation_failed": "生成语音失败: @message", "anki.tts.voice_settings": "配音设置", "anki.tts.volume": "音量", "anki.wereader_card.added_highlight_at": "@time添加划线", "anki.wereader_card.answer_cloze": "答案挖空", "anki.wereader_card.api_error": "错误: @message", "anki.wereader_card.api_request": "请求: @uri", "anki.wereader_card.api_request_failed": "请求失败: @statusCode", "anki.wereader_card.ascending_order": "升序", "anki.wereader_card.at_least_one_export_type": "至少选择一种导出类型", "anki.wereader_card.at_least_one_note_type": "至少选择一种笔记类型", "anki.wereader_card.cannot_get_vid": "无法从 <PERSON>ie 中获取 wr_vid", "anki.wereader_card.cloze_grammar": "挖空语法", "anki.wereader_card.continue_processing_highlights": "继续处理划线内容", "anki.wereader_card.cookie_cannot_empty": "Cookie不能为空", "anki.wereader_card.cookie_expired_refreshed": "Cookie已过期，尝试刷新Cookie成功", "anki.wereader_card.cookie_placeholder": "输入<PERSON><PERSON>", "anki.wereader_card.cookie_refresh_error": "刷新Cookie失败: @message", "anki.wereader_card.cookie_refresh_failed": "尝试刷新Cookie失败", "anki.wereader_card.cookie_refreshed_retry": "已刷新Cookie，重试提取笔记", "anki.wereader_card.cookie_validation_error": "<PERSON>ie验证失败: @error", "anki.wereader_card.cookie_validation_failed": "Cookie验证失败", "anki.wereader_card.descending_order": "降序", "anki.wereader_card.enable_separator": "启用分隔符", "anki.wereader_card.end_time": "结束时间", "anki.wereader_card.export_format": "导出格式", "anki.wereader_card.export_type": "导出类型", "anki.wereader_card.extract_hot_failed": "提取热门划线失败: @error", "anki.wereader_card.extract_notes_failed": "提取笔记失败: @error", "anki.wereader_card.feature_description": "支持将微信读书笔记制卡，支持问答、挖空制卡，也支持批量导出微信读书笔记", "anki.wereader_card.function_description": "功能说明", "anki.wereader_card.generate_excel_failed": "生成Excel文件失败", "anki.wereader_card.get_all_hot_format_error": "获取全部热门划线失败: 返回数据格式不正确", "anki.wereader_card.get_all_hot_success": "成功获取全部热门划线", "anki.wereader_card.get_book_info_failed": "获取书籍详情失败: @message", "anki.wereader_card.get_bookshelf_failed": "获取书架失败: @message", "anki.wereader_card.get_bookshelf_status_failed": "获取书架失败: @statusCode", "anki.wereader_card.get_chapters_failed": "获取书籍目录失败: @message", "anki.wereader_card.get_chapters_format_error": "获取书籍目录失败: 返回数据格式不正确", "anki.wereader_card.get_highlights_failed": "获取划线失败: @message", "anki.wereader_card.get_hot_failed": "获取热门划线失败: @message", "anki.wereader_card.get_hot_unknown_error": "获取热门划线时发生未知错误: @error", "anki.wereader_card.get_notebook_failed": "获取笔记本失败: @message, @data, cookie: @cookie", "anki.wereader_card.get_progress_failed": "获取阅读进度信息失败: @message", "anki.wereader_card.get_reviews_failed": "获取个人想法失败: @message", "anki.wereader_card.getting_all_hot": "正在获取全部 @count 条热门划线...", "anki.wereader_card.getting_hot_count": "正在获取热门划线总数...", "anki.wereader_card.highlight_notes": "划线笔记", "anki.wereader_card.highlights_api_format_error": "划线API返回数据格式不正确: @result", "anki.wereader_card.highlights_api_format_error_simple": "划线API返回数据格式不正确", "anki.wereader_card.hot_api_format_error": "热门划线API返回数据格式不正确", "anki.wereader_card.hot_highlights": "热门划线", "anki.wereader_card.hot_highlights_format_error": "获取热门划线失败: 返回数据格式不正确", "anki.wereader_card.input_book": "输入书籍", "anki.wereader_card.input_cloze_grammar": "输入挖空语法", "anki.wereader_card.input_separator": "输入分隔符", "anki.wereader_card.input_tags": "输入标签", "anki.wereader_card.limit_time_range": "限定时间范围", "anki.wereader_card.my_notes": "我的笔记", "anki.wereader_card.no_hot_highlights": "没有热门划线，返回初始请求结果", "anki.wereader_card.note_card_tab": "笔记制卡", "anki.wereader_card.note_export_tab": "笔记导出", "anki.wereader_card.note_source": "笔记来源", "anki.wereader_card.note_type": "笔记类型", "anki.wereader_card.output_directory": "输出目录", "anki.wereader_card.people_highlighted": "@count人划线", "anki.wereader_card.processing": "正在处理...", "anki.wereader_card.published_at": "@time发表", "anki.wereader_card.published_thought_at": "@time发表想法：", "anki.wereader_card.review_notes": "想法笔记", "anki.wereader_card.reviews_api_format_error": "个人想法API返回数据格式不正确: @result", "anki.wereader_card.reviews_api_format_error_simple": "个人想法API返回数据格式不正确", "anki.wereader_card.select_cloze_grammar": "选择挖空语法", "anki.wereader_card.select_end_time": "选择结束时间", "anki.wereader_card.select_output_directory": "请选择输出目录", "anki.wereader_card.select_separator": "选择分隔符", "anki.wereader_card.select_start_time": "选择开始时间", "anki.wereader_card.select_tags": "选择标签", "anki.wereader_card.select_target_book": "选择目标书籍", "anki.wereader_card.separator": "分隔符", "anki.wereader_card.sort_by_chapter": "按章节", "anki.wereader_card.sort_by_create_time": "按添加时间", "anki.wereader_card.sort_by_popularity": "按热度", "anki.wereader_card.sort_direction": "排序方向", "anki.wereader_card.sort_method": "排序方式", "anki.wereader_card.start_time": "开始时间", "anki.wereader_card.target_book": "目标书籍", "anki.wereader_card.title": "微信读书制卡", "anki.word_card.answer_after_option": "答案位于选项后", "anki.word_card.answer_cloze": "答案挖空", "anki.word_card.answer_file_fields": "答案文件包含字段", "anki.word_card.answer_in_question": "答案位于题干中", "anki.word_card.at_least_one_cloze_grammar": "至少选择一种挖空语法", "anki.word_card.at_least_one_cloze_style": "至少选择一种挖空样式", "anki.word_card.blue_color": "蓝色(#0000FF)", "anki.word_card.bold": "粗体", "anki.word_card.card_template": "卡片模版", "anki.word_card.choice_tab": "选择题", "anki.word_card.cloze_grammar": "挖空语法", "anki.word_card.cloze_style": "挖空样式", "anki.word_card.cloze_tab": "挖空题", "anki.word_card.feature_description": "支持docx格式文件制卡，支持挖空卡、问答卡、选择题、判断题制作，支持多种制卡模式", "anki.word_card.file_path_not_exist": "文件路径不存在", "anki.word_card.fixed_options_true_false": "对||错", "anki.word_card.free_guess": "自由猜", "anki.word_card.function_description": "功能说明", "anki.word_card.green_color": "绿色(#00FF00)", "anki.word_card.guru_import": "<PERSON>导入", "anki.word_card.highlight_color": "高亮颜色", "anki.word_card.input_color_placeholder": "输入颜色, 如#FF0000", "anki.word_card.input_subdeck_prefix": "输入子牌组前缀", "anki.word_card.input_tags": "输入标签", "anki.word_card.italic": "斜体", "anki.word_card.judge_tab": "判断题", "anki.word_card.mask_all_guess_all": "遮全猜全", "anki.word_card.mask_all_guess_one": "遮全猜一", "anki.word_card.mask_one_guess_one": "遮一猜一", "anki.word_card.none": "无", "anki.word_card.normal_separation": "普通分隔", "anki.word_card.one_cloze_per_card": "一空一卡", "anki.word_card.please_select_answer_file": "请选择答案文件", "anki.word_card.please_select_file": "请选择文件", "anki.word_card.please_select_file_not_directory": "请选择文件, 不是目录", "anki.word_card.please_select_files": "请选择文件!", "anki.word_card.please_select_kevin_choice_template": "请选择【Kevin Choice Card v2】模板", "anki.word_card.please_select_kevin_text_cloze_template": "请选择【Kevin Text Cloze v3】模板", "anki.word_card.please_select_question_answer_files": "请选择问题文件和答案文件!", "anki.word_card.please_select_question_file": "请选择问题文件", "anki.word_card.please_set_answers_field_pattern": "请设置Answers字段的匹配模式!", "anki.word_card.please_set_front_field_pattern": "请设置Front字段的匹配模式!", "anki.word_card.please_set_question_field_pattern": "请设置Question字段的匹配模式!", "anki.word_card.processing_question": "正在处理第 @current 题", "anki.word_card.qa_tab": "问答题", "anki.word_card.question_answer_count_mismatch": "牌组 @deck 中的问题数量(@questionCount)与答案数量(@answerCount)不匹配!", "anki.word_card.question_answer_cross_file": "问题答案不同文件", "anki.word_card.question_answer_cross_file_choice": "问题答案不同文件", "anki.word_card.question_answer_same_file": "问题答案同文件", "anki.word_card.question_file_fields": "问题文件包含字段", "anki.word_card.question_single_line": "问题单行", "anki.word_card.red_color": "红色(#FF0000)", "anki.word_card.regex_separation": "正则分隔", "anki.word_card.restore_params": "恢复参数", "anki.word_card.save_params": "保存参数", "anki.word_card.select_answer_file_fields": "选择答案文件包含字段", "anki.word_card.select_answer_file_fields_required": "请选择答案文件包含字段", "anki.word_card.select_answer_file_placeholder": "请选择答案文件", "anki.word_card.select_card_template": "选择卡片模版", "anki.word_card.select_color": "选择颜色", "anki.word_card.select_question_file_fields": "选择问题文件包含字段", "anki.word_card.select_question_file_fields_required": "请选择问题文件包含字段", "anki.word_card.select_subdeck_prefix": "选择子牌组前缀", "anki.word_card.select_tags": "选择标签", "anki.word_card.strikethrough": "删除线", "anki.word_card.subdeck_prefix": "子牌组前缀", "anki.word_card.text_color": "文本颜色", "anki.word_card.text_color_option": "文本颜色", "anki.word_card.text_highlight": "文本高亮", "anki.word_card.title": "Word制卡", "anki.word_card.underline": "下划线", "anki.word_card.yellow_color": "黄色(#FFFF00)", "ankiConfig.ankiConnectAddress": "AnkiConnect地址", "ankiConfig.ankiPath": "<PERSON><PERSON>路径", "ankiConfig.autoStartAnki": "自启Anki", "ankiConfig.cardMode": "制卡模式", "ankiConfig.ffmpegPath": "FFmpeg路径", "ankiConfig.outputDirectory": "输出目录", "ankiConfig.outputDirectoryPlaceholder": "apkg输出目录", "ankiConfig.pdfReaderPath": "PDF阅读器路径", "ankiConfig.selectAnkiPath": "请选择Anki路径", "ankiConfig.selectCardMode": "选择制卡模式", "ankiConfig.selectFfmpegPath": "请选择FFmpeg路径", "ankiConfig.selectPdfReaderPath": "请选择PDF阅读器路径", "ankiConfig.title": "首选项", "common.colorPicker.cancel": "取消", "common.colorPicker.confirm": "确定", "common.colorPicker.selectColor": "选择颜色", "common.completed": "已完成", "common.error": "失败", "common.fileSelect.clearAll": "清空所有", "common.fileSelect.customDirectory": "自定义目录", "common.fileSelect.directoryCannotBeEmpty": "目录路径不能为空", "common.fileSelect.directoryNotExist": "目录路径不存在", "common.fileSelect.fileCannotBeEmpty": "文件路径不能为空", "common.fileSelect.fileNotExist": "文件路径不存在", "common.fileSelect.overwriteOriginal": "覆盖原文件", "common.fileSelect.pleaseSelectFiles": "请选择符合 @format 格式的文件", "common.fileSelect.sameDirectory": "同级目录", "common.fileSelect.selectDirectoryNotFile": "请选择目录, 不是文件", "common.fileSelect.selectFileNotDirectory": "请选择文件, 不是目录", "common.fileSelect.selectedFiles": "已选择 @count 个文件", "common.ui.emptyList": "列表为空", "common.ui.fieldMapping": "字段映射", "common.ui.keepPrefix": "保留前缀", "common.ui.matchMode": "匹配模式", "common.ui.searchPlaceholder": "搜索或输入", "common.ui.selectOrEnter": "选择或输入", "common.ui.templateField": "模版字段", "home.sections.ankiCards": "<PERSON><PERSON>制卡", "home.sections.ankiEnhance": "<PERSON><PERSON>增强", "home.sections.conversion": "转换工具", "home.sections.notes": "效率工具", "home.sections.pdfEdit": "PDF编辑", "home.tool.vocab_card": "单词制卡", "home.tools.aiCard": "AI制卡", "home.tools.ankiSync": "<PERSON><PERSON>步", "home.tools.cardOcr": "卡片OCR", "home.tools.cardTts": "卡片朗读", "home.tools.card_media_manager": "媒体管理", "home.tools.deckManager": "牌组管理", "home.tools.docxToHtml": "DOCX转HTML", "home.tools.epubToPdf": "EPUB转PDF", "home.tools.excelCard": "Excel制卡", "home.tools.flashNote": "闪念笔记", "home.tools.imageCard": "图片制卡", "home.tools.image_ocr": "图片OCR", "home.tools.imgToPdf": "图片转PDF", "home.tools.markdownCard": "Markdown制卡", "home.tools.mdToHtml": "MD转HTML", "home.tools.mediaCard": "多媒体制卡", "home.tools.mindmapCard": "导图制卡", "home.tools.mobiToPdf": "MOBI转PDF", "home.tools.mubuCard": "幕布制卡", "home.tools.pdfAnnot": "PDF批注", "home.tools.pdfBackground": "PDF背景", "home.tools.pdfBookmark": "PDF书签", "home.tools.pdfCard": "PDF制卡", "home.tools.pdfCombine": "PDF组合", "home.tools.pdfCrop": "PDF裁剪", "home.tools.pdfCut": "PDF分割", "home.tools.pdfDelete": "PDF删除", "home.tools.pdfEncrypt": "PDF加密", "home.tools.pdfExpand": "PDF延展", "home.tools.pdfExtract": "PDF提取", "home.tools.pdfInsert": "PDF插入", "home.tools.pdfMerge": "PDF合并", "home.tools.pdfMeta": "PDF元信息", "home.tools.pdfNote": "PDF笔记", "home.tools.pdfPageNumber": "PDF页码", "home.tools.pdfReorder": "PDF重排", "home.tools.pdfRotate": "PDF旋转", "home.tools.pdfScale": "PDF缩放", "home.tools.pdfSplit": "PDF拆分", "home.tools.pdfToDocx": "PDF转DOCX", "home.tools.pdfToImg": "PDF转图片", "home.tools.pdfToImgPdf": "PDF转图片型", "home.tools.pdfWatermark": "PDF水印", "home.tools.pdf_ocr": "PDF OCR", "home.tools.recoverPermission": "恢复权限", "home.tools.textCard": "文本制卡", "home.tools.videoNote": "视频笔记", "home.tools.wechatReaderCard": "微信读书制卡", "home.tools.wordCard": "Word制卡", "hotkey.action.cancel": "取消", "hotkey.action.confirm": "确认", "hotkey.dialog.title": "录入快捷键", "hotkey.error.enterShortcut": "请先录入快捷键", "hotkey.error.requireModifier": "快捷键必须包含至少一个修饰键(如Ctrl、Alt、Shift等)", "hotkey.error.title": "失败", "license.activation.failed": "激活失败", "license.activation.success": "激活成功", "license.faq.lifetime_10years.content": "终生套餐激活码每10年为周期发放, 到期可免费续期。", "license.faq.lifetime_10years.title": "为什么终生套餐激活码只有10年?", "license.faq.reinstall.content": "不需要，只有当您需要重装系统或换新设备时需要注销。如果您重装软件后失去激活状态，重新再次激活即可。同一设备重复激活不会额外消耗激活次数。", "license.faq.reinstall.title": "卸载重装软件是否需要注销?", "license.faq.why_activate.content": "本软件功能丰富强大, 界面美观舒适，多端离线可用, 无需担心隐私泄露, 纯净无广, 众多PDF处理功能可直接免费无限制使用, 是您日常办公和学习的好帮手。但为了软件健康发展, 持续为用户带来更优质的使用体验, 内设部分付费功能(如Anki制卡、视频笔记等), 您可通过购买激活码表示支持, 并解锁软件的全部功能。注意：本软件仅限个人用户合理范围内使用，使用本软件产生直接或间接盈利等商业行为请联系作者单独购买商用授权。", "license.faq.why_activate.title": "为什么需要激活?", "license.request.failed": "失败", "license.trial.failed": "获取试用授权失败", "license.trial.success": "试用激活成功", "license.unregister.failed": "注销失败", "license.unregister.success": "注销成功", "navigation.home": "首页", "navigation.settings": "设置", "paywall.benefitsTitle": "会员专享权益", "paywall.features.aiCard": "高级AI制卡", "paywall.features.eudicCard": "欧路词典制卡", "paywall.features.excelCard": "Excel制卡", "paywall.features.fastSync": "高速同步服务器", "paywall.features.imageCard": "图片挖空制卡", "paywall.features.logseqCard": "Logseq制卡", "paywall.features.markdownCard": "Markdown制卡", "paywall.features.mubuCard": "幕布制卡", "paywall.features.multimediaCard": "多媒体制卡", "paywall.features.multipleChoiceCard": "选择题批量制卡", "paywall.features.notionCard": "Notion制卡", "paywall.features.obsidianCard": "Obsidian制卡", "paywall.features.pdfCard": "PDF制卡", "paywall.features.prioritySupport": "优先技术支持", "paywall.features.quickNote": "闪念笔记功能", "paywall.features.siyuanCard": "思源笔记制卡", "paywall.features.textCard": "文本制卡", "paywall.features.textToSpeech": "文本转语音功能", "paywall.features.videoNote": "视频笔记功能", "paywall.features.wechatReadCard": "微信读书制卡", "paywall.features.wordCard": "Word制卡", "paywall.features.wordListCard": "单词制卡", "paywall.features.xmindCard": "Xmind制卡", "paywall.features.zhixiCard": "知犀导图制卡", "paywall.headerSubtitle": "解锁所有高级功能，神器在手，学习无忧", "paywall.headerTitle": "PDF Guru <PERSON> 高级版", "paywall.lifetimeSubscription": "终身会员", "paywall.monthlySubscription": "月度订阅", "paywall.mostPopular": "最受欢迎", "paywall.oneTimePurchase": "一次性付费", "paywall.perMonth": "每月", "paywall.perYear": "每年", "paywall.privacyPolicy": "隐私政策", "paywall.purchase": "立即购买", "paywall.restorePurchase": "恢复购买", "paywall.selectPlan": "请选择套餐", "paywall.subscriptionDetails": "点击购买即表示您已阅读并同意《用户协议》和《隐私政策》，付款将从您的AppleID账户中扣除。在每个续期日期前至少一天，您可随时在Apple Store的账户设置中取消订阅。", "paywall.subscriptionNotice": "订阅须知", "paywall.termsOfService": "用户协议", "paywall.title": "会员购买", "paywall.yearlySubscription": "年度订阅", "progress.button.open": "打开", "progress.button.play": "试听", "progress.button.share": "分享", "progress.dialog.title": "处理进度", "progress.error.cannotGetButtonPosition": "无法获取按钮位置", "progress.error.cannotGetContext": "无法获取上下文", "progress.error.cannotOpenFile": "无法打开文件", "progress.error.cannotOpenFolder": "无法打开文件夹", "progress.error.cannotShareFile": "无法分享文件", "progress.error.filePathNotExist": "文件路径不存在", "progress.error.shareDirectoryNotSupported": "不支持分享目录", "progress.error.title": "错误", "progress.status.completed": "已完成", "progress.status.fileSavedTo": "文件已保存至", "progress.status.initializing": "初始化...", "progress.status.processing": "处理中...", "progress.status.totalProgress": "总进度", "progress.time.elapsedTime": "用时", "progress.time.startTime": "开始时间", "service.browser.cannotConnect": "无法连接到浏览器扩展", "service.browser.cannotOpenVideo": "无法在浏览器中打开视频，请检查连接状态", "service.browser.commandSent": "命令已发送到浏览器扩展", "service.browser.extensionIdentified": "浏览器扩展识别成功", "service.browser.extensionInstallPrompt": "请确保浏览器扩展已安装并连接到应用", "service.browser.extensionRequired": "需要连接浏览器扩展", "service.browser.navigationFailed": "浏览器导航失败", "service.browser.openingVideo": "正在打开浏览器视频", "service.browser.videoWillOpen": "视频将在浏览器中打开并跳转到指定时间", "service.screenshot.browserFailed": "浏览器截图失败", "service.screenshot.dataEmpty": "截图数据为空", "service.screenshot.failed": "截图失败", "service.screenshot.saved": "截图已保存到剪贴板", "service.timestamp.browserExtensionFailed": "浏览器扩展时间戳生成失败", "service.timestamp.generationFailed": "时间戳生成失败", "service.timestamp.linkCopied": "时间戳链接已复制", "service.timestamp.linkCopiedToClipboard": "时间戳链接已复制到剪贴板", "service.timestamp.linkSaved": "链接已保存到剪贴板", "service.video.cannotRetrieveInfo": "无法获取视频信息", "service.video.infoRetrievalFailed": "视频信息获取失败", "service.video.jumpedToTime": "已跳转到指定时间", "service.video.jumpedToTimestamp": "浏览器视频已跳转到指定时间戳", "service.video.openedAndJumped": "视频已成功在浏览器中打开并跳转到指定时间", "service.video.openedInBrowser": "视频已在浏览器中打开", "settings.aboutAndHelp": "关于与帮助", "settings.activate": "激活", "settings.cardMode.directAnki": "直连Anki", "settings.cardMode.directAnkidroid": "直连ankidroid", "settings.cardMode.exportApkg": "导出Apkg", "settings.displayLanguage": "显示语言", "settings.language": "语言", "settings.launchAtStartup": "开机自启", "settings.preferences": "首选项", "settings.saved": "设置已保存", "settings.theme.dark": "深色", "settings.theme.light": "浅色", "settings.theme.system": "跟随系统", "settings.themeSettings": "主题设置", "settings.userCenter": "用户中心", "toolbox.annotation.annotationDelete": "批注删除", "toolbox.annotation.annotationExport": "批注导出", "toolbox.annotation.annotationFile": "批注文件", "toolbox.annotation.annotationFlatten": "批注扁平", "toolbox.annotation.annotationImport": "批注导入", "toolbox.annotation.deleteTab": "删除批注", "toolbox.annotation.description": "导入、导出或删除PDF文件中的批注", "toolbox.annotation.exportFormat": "导出格式", "toolbox.annotation.exportTab": "导出批注", "toolbox.annotation.flattenTab": "扁平批注", "toolbox.annotation.importFormat": "导入格式", "toolbox.annotation.importTab": "导入批注", "toolbox.annotation.title": "PDF批注", "toolbox.background.backgroundColor": "背景颜色", "toolbox.background.backgroundImage": "背景图片", "toolbox.background.backgroundType": "背景类型", "toolbox.background.colorBackground": "纯色背景", "toolbox.background.completed": "已完成", "toolbox.background.description": "为PDF文件添加纯色背景或图片背景", "toolbox.background.fileNotExist": "文件不存在", "toolbox.background.generateBackgroundFailed": "生成背景PDF失败", "toolbox.background.imageBackground": "图片背景", "toolbox.background.imageFileNotExist": "图片文件不存在", "toolbox.background.imageNotSelected": "未选择图片", "toolbox.background.opacity": "不透明度", "toolbox.background.opacityPlaceholder": "输入不透明度，位于0-1之间", "toolbox.background.processingFile": "处理文件", "toolbox.background.scale": "缩放比例", "toolbox.background.scalePlaceholder": "输入缩放比例", "toolbox.background.title": "PDF背景", "toolbox.background.unknownBackgroundType": "未知背景类型", "toolbox.background.xOffset": "X偏移量", "toolbox.background.xOffsetPlaceholder": "输入水平偏移", "toolbox.background.yOffset": "Y偏移量", "toolbox.background.yOffsetPlaceholder": "输入垂直偏移", "toolbox.bookmark.bookmarkFile": "书签文件", "toolbox.bookmark.bookmarkFileEmpty": "书签文件不能为空", "toolbox.bookmark.deleteBookmarks": "删除书签", "toolbox.bookmark.deleteTab": "删除书签", "toolbox.bookmark.description": "导入、导出或删除PDF文件中的书签", "toolbox.bookmark.exportBookmarks": "导出书签", "toolbox.bookmark.exportFormat": "导出格式", "toolbox.bookmark.exportTab": "导出书签", "toolbox.bookmark.importBookmarks": "导入书签", "toolbox.bookmark.importTab": "导入书签", "toolbox.bookmark.title": "PDF书签", "toolbox.combine.col_lr": "列优先(自左向右)", "toolbox.combine.col_rl": "列优先(自右向左)", "toolbox.combine.completed": "已完成", "toolbox.combine.description": "将原始PDF文件多个页面组合成一个页面", "toolbox.combine.landscape": "横向", "toolbox.combine.layout_order": "布局顺序", "toolbox.combine.numCols": "列数", "toolbox.combine.numColsPlaceholder": "输入列数", "toolbox.combine.numRows": "行数", "toolbox.combine.numRowsPlaceholder": "输入行数", "toolbox.combine.portrait": "纵向", "toolbox.combine.row_lr": "行优先(自左向右)", "toolbox.combine.row_rl": "行优先(自右向左)", "toolbox.combine.title": "PDF组合", "toolbox.common.enterPageRange": "请输入正确的页码范围, 例如: 1-3,5-7", "toolbox.common.error_with_msg": "处理失败: @error", "toolbox.common.failure": "失败", "toolbox.common.fileProcessSuccess": "处理文件成功", "toolbox.common.fileSelect.error": "请选择PDF文件", "toolbox.common.functionDescription": "功能说明", "toolbox.common.inputFile": "输入文件", "toolbox.common.inputFilePlaceholder": "输入文件绝对路径，或拖拽文件到此处", "toolbox.common.operationFailed": "处理失败", "toolbox.common.output.error": "请选择输出目录", "toolbox.common.outputDir": "输出目录", "toolbox.common.outputDirectory": "输出目录", "toolbox.common.outputLocation": "输出位置", "toolbox.common.pageRange": "页码范围", "toolbox.common.pageRangePlaceholder": "输入页码范围，留空表示全部", "toolbox.common.process.completed": "已完成", "toolbox.common.process.failed": "操作失败", "toolbox.common.process.processFailed": "处理文件失败", "toolbox.common.process.running": "处理文件中...", "toolbox.common.process.success": "处理文件成功", "toolbox.common.processing": "正在处理...", "toolbox.common.selectOutputLocation": "选择输出位置", "toolbox.common.selectPdfFiles": "请选择一个PDF文件", "toolbox.common.submit": "提交", "toolbox.convert.common.inputFile": "输入文件", "toolbox.convert.common.inputFilePlaceholder": "输入文件绝对路径，或拖拽文件到此处", "toolbox.convert.common.outputDirectory": "输出目录", "toolbox.convert.common.outputLocation": "输出位置", "toolbox.convert.common.selectOutputLocation": "选择输出位置", "toolbox.convert.common.submit": "提交", "toolbox.convert.docx2html.description": "支持将DOCX转换为HTML", "toolbox.convert.docx2html.title": "DOCX转HTML", "toolbox.convert.docx2pdf.description": "支持将DOCX转换为PDF", "toolbox.convert.docx2pdf.title": "DOCX转PDF", "toolbox.convert.epub2pdf.description": "支持将EPUB转换为PDF", "toolbox.convert.epub2pdf.title": "EPUB转PDF", "toolbox.convert.html2pdf.customFont": "自定义字体", "toolbox.convert.html2pdf.description": "支持将HTML文件转换为PDF文档", "toolbox.convert.html2pdf.font": "字体", "toolbox.convert.html2pdf.fontFile": "字体文件", "toolbox.convert.html2pdf.fontFilePlaceholder": "输入字体文件绝对路径，或拖拽文件到此处", "toolbox.convert.html2pdf.fontSize": "字号", "toolbox.convert.html2pdf.fontSizeError1": "请输入字号， 例如: 12", "toolbox.convert.html2pdf.fontSizeError2": "请输入浮点数", "toolbox.convert.html2pdf.fontSizePlaceholder": "输入字号", "toolbox.convert.html2pdf.selectFont": "选择字体", "toolbox.convert.html2pdf.title": "HTML转PDF", "toolbox.convert.img2pdf.description": "支持将图片转换为PDF", "toolbox.convert.img2pdf.mergeFiles": "合并文件", "toolbox.convert.img2pdf.orientation": "纸张方向", "toolbox.convert.img2pdf.paperSize": "纸张大小", "toolbox.convert.img2pdf.selectOrientation": "请选择方向", "toolbox.convert.img2pdf.selectPaperSize": "请选择纸张大小", "toolbox.convert.img2pdf.sortBy": "排序依据", "toolbox.convert.img2pdf.sortDirection": "排序方向", "toolbox.convert.img2pdf.sortDirections.ascending": "升序", "toolbox.convert.img2pdf.sortDirections.descending": "降序", "toolbox.convert.img2pdf.sortOptions.byCreateDate": "创建时间", "toolbox.convert.img2pdf.sortOptions.byModDate": "修改时间", "toolbox.convert.img2pdf.sortOptions.byName": "文件名顺序", "toolbox.convert.img2pdf.sortOptions.byNumberPrefix": "文件名开头编号", "toolbox.convert.img2pdf.sortOptions.byNumberSuffix": "文件名末尾编号", "toolbox.convert.img2pdf.sortOptions.bySelection": "添加顺序", "toolbox.convert.img2pdf.title": "图片转PDF", "toolbox.convert.md2html.description": "支持将Markdown转换为HTML", "toolbox.convert.md2html.title": "MD转HTML", "toolbox.convert.md2pdf.description": "支持将Markdown文件转换为PDF文档", "toolbox.convert.md2pdf.title": "Markdown转PDF", "toolbox.convert.mobi2pdf.description": "支持将MOBI转换为PDF", "toolbox.convert.mobi2pdf.title": "MOBI转PDF", "toolbox.convert.ofd2pdf.description": "支持将OFD转换为PDF", "toolbox.convert.ofd2pdf.title": "OFD转PDF", "toolbox.convert.pdf2docx.description": "支持将PDF转换为DOCX", "toolbox.convert.pdf2docx.title": "PDF转DOCX", "toolbox.convert.pdf2img.advancedScaling": "高级缩放选项", "toolbox.convert.pdf2img.autoScaleToA4": "自动将超大页面缩放至A4尺寸", "toolbox.convert.pdf2img.description": "支持将PDF指定页面转换为图片", "toolbox.convert.pdf2img.grayscale": "转灰度", "toolbox.convert.pdf2img.maxHeightPixels": "最大高度(像素)", "toolbox.convert.pdf2img.maxHeightPixelsPlaceholder": "最大高度像素值, 默认4000", "toolbox.convert.pdf2img.maxPixelsError": "请输入大于0的有效像素值", "toolbox.convert.pdf2img.maxWidthPixels": "最大宽度(像素)", "toolbox.convert.pdf2img.maxWidthPixelsPlaceholder": "最大宽度像素值, 默认3000", "toolbox.convert.pdf2img.pageRange": "页码范围", "toolbox.convert.pdf2img.pageRangeError": "请输入正确的页码范围, 例如: 1-3,5-7", "toolbox.convert.pdf2img.pageRangePlaceholder": "输入页码范围, 留空表示全部", "toolbox.convert.pdf2img.resolution": "分辨率(DPI)", "toolbox.convert.pdf2img.resolutionError": "请输入正确的分辨率, 例如: 300", "toolbox.convert.pdf2img.resolutionPlaceholder": "输入分辨率, 默认300", "toolbox.convert.pdf2img.title": "PDF转图片", "toolbox.convert.pdf2img_pdf.description": "支持将PDF转换为图片型PDF", "toolbox.convert.pdf2img_pdf.title": "PDF转图片型", "toolbox.crop.cropType": "裁剪类型", "toolbox.crop.description": "裁剪PDF文件中的页面", "toolbox.crop.expandMode": "扩展模式", "toolbox.crop.keepPaperSize": "保持页面尺寸", "toolbox.crop.margin.bottom": "下边距", "toolbox.crop.margin.bottomPlaceholder": "输入下边距", "toolbox.crop.margin.left": "左边距", "toolbox.crop.margin.leftPlaceholder": "输入左边距", "toolbox.crop.margin.right": "右边距", "toolbox.crop.margin.rightPlaceholder": "输入右边距", "toolbox.crop.margin.top": "上边距", "toolbox.crop.margin.topPlaceholder": "输入上边距", "toolbox.crop.outputFormat": "输出格式", "toolbox.crop.stem_append": "_裁剪", "toolbox.crop.title": "PDF裁剪", "toolbox.crop.unit": "单位", "toolbox.cropTypeOptions.annotate": "矩形注释裁剪", "toolbox.cropTypeOptions.margin": "页边距裁剪", "toolbox.cut.custom.hBreakpoints": "水平分割点", "toolbox.cut.custom.hBreakpointsInvalid": "水平分割点格式无效", "toolbox.cut.custom.hBreakpointsPlaceholder": "输入水平分割点，例如：0.4,0.7", "toolbox.cut.custom.vBreakpoints": "垂直分割点", "toolbox.cut.custom.vBreakpointsInvalid": "垂直分割点格式无效", "toolbox.cut.custom.vBreakpointsPlaceholder": "输入垂直分割点，例如：0.4,0.7", "toolbox.cut.cutOptions.custom": "自定义分割", "toolbox.cut.cutOptions.grid": "网格分割", "toolbox.cut.cutOptions.page": "页分割", "toolbox.cut.cutType": "分割类型", "toolbox.cut.description": "将原始PDF文件(的指定页面)分割成多个页面", "toolbox.cut.grid.numCols": "列数", "toolbox.cut.grid.numColsPlaceholder": "输入列数", "toolbox.cut.grid.numColsRequired": "请输入列数，例如: 1", "toolbox.cut.grid.numRows": "行数", "toolbox.cut.grid.numRowsPlaceholder": "输入行数", "toolbox.cut.grid.numRowsRequired": "请输入行数，例如: 1", "toolbox.cut.page.margin.bottom": "下边距", "toolbox.cut.page.margin.bottomPlaceholder": "输入下边距", "toolbox.cut.page.margin.invalid": "请输入大于等于0的数字", "toolbox.cut.page.margin.left": "左边距", "toolbox.cut.page.margin.leftPlaceholder": "输入左边距", "toolbox.cut.page.margin.right": "右边距", "toolbox.cut.page.margin.rightPlaceholder": "输入右边距", "toolbox.cut.page.margin.top": "上边距", "toolbox.cut.page.margin.topPlaceholder": "输入上边距", "toolbox.cut.page.orientation": "方向", "toolbox.cut.page.pageSize": "纸张尺寸", "toolbox.cut.page.selectOrientation": "选择方向", "toolbox.cut.page.selectPageSize": "选择纸张尺寸", "toolbox.cut.title": "PDF分割", "toolbox.decrypt.password": "密码", "toolbox.decrypt.passwordPlaceholder": "请输入密码", "toolbox.delete.blankTab": "空白页面", "toolbox.delete.description": "支持删除PDF指定页面", "toolbox.delete.stem_append": "页面删除", "toolbox.delete.title": "PDF删除", "toolbox.encrypt.decryptTab": "移除密码", "toolbox.encrypt.description": "支持将PDF文件加密或解密", "toolbox.encrypt.encryptTab": "设置密码", "toolbox.encrypt.fileNameAppend.decrypt": "解密", "toolbox.encrypt.fileNameAppend.encrypt": "加密", "toolbox.encrypt.mode.decrypt": "解密", "toolbox.encrypt.mode.encrypt": "加密", "toolbox.encrypt.passwordError.mismatch": "两次输入的用户密码不一致", "toolbox.encrypt.passwordError.permissionMismatch": "两次输入的权限密码不一致", "toolbox.encrypt.passwordError.permissionRequired": "请输入权限密码", "toolbox.encrypt.passwordError.required": "请输入用户密码", "toolbox.encrypt.passwordError.validation": "密码验证失败", "toolbox.encrypt.passwordType": "密码类型", "toolbox.encrypt.passwordTypeRequired": "至少选择一种密码类型", "toolbox.encrypt.permission.assembleDocument": "组装", "toolbox.encrypt.permission.copyContent": "复制", "toolbox.encrypt.permission.editAnnotations": "编辑注释", "toolbox.encrypt.permission.editContent": "编辑内容", "toolbox.encrypt.permission.fillFields": "填写表单", "toolbox.encrypt.permission.print": "打印", "toolbox.encrypt.title": "PDF加密", "toolbox.encrypt.type.permission": "权限密码", "toolbox.encrypt.type.user": "用户密码", "toolbox.encrypt.user.confirmEmpty": "确认密码不能为空", "toolbox.encrypt.user.confirmPassword": "用户密码确认", "toolbox.encrypt.user.confirmPasswordPlaceholder": "请再次输入用户密码", "toolbox.encrypt.user.password": "用户密码", "toolbox.encrypt.user.passwordEmpty": "密码不能为空", "toolbox.encrypt.user.passwordMismatch": "两次密码输入不一致", "toolbox.encrypt.user.passwordPlaceholder": "请输入用户密码", "toolbox.encrypt.user.passwordTooShort": "密码长度不能小于6位", "toolbox.expand.blank.margin.bottom": "下边距", "toolbox.expand.blank.margin.bottomPlaceholder": "输入下边距", "toolbox.expand.blank.margin.invalid": "请输入大于等于0的数字", "toolbox.expand.blank.margin.left": "左边距", "toolbox.expand.blank.margin.leftPlaceholder": "输入左边距", "toolbox.expand.blank.margin.right": "右边距", "toolbox.expand.blank.margin.rightPlaceholder": "输入右边距", "toolbox.expand.blank.margin.top": "上边距", "toolbox.expand.blank.margin.topPlaceholder": "输入上边距", "toolbox.expand.blank.unit": "单位", "toolbox.expand.description": "将原始PDF文件(的指定页面)按照给定参数进行延展", "toolbox.expand.direction.bottom": "下", "toolbox.expand.direction.left": "左", "toolbox.expand.direction.right": "右", "toolbox.expand.direction.top": "上", "toolbox.expand.expandType": "延展类型", "toolbox.expand.file.bgFile": "背景文件", "toolbox.expand.file.bgFilePlaceholder": "输入背景文件绝对路径，或拖拽文件到此处", "toolbox.expand.file.direction": "方向", "toolbox.expand.fileNameAppend": "延展", "toolbox.expand.mode.blank": "空白延展", "toolbox.expand.mode.file": "文件延展", "toolbox.expand.title": "PDF延展", "toolbox.expand.unit.pt": "像素", "toolbox.expand.unit.ratio": "比例", "toolbox.extract.description": "支持提取PDF页面、文本、图片等", "toolbox.extract.extractMode": "提取模式", "toolbox.extract.fileNameAppend": "提取页面", "toolbox.extract.imageAppend": "提取图片", "toolbox.extract.mode.image": "图片", "toolbox.extract.mode.page": "页面", "toolbox.extract.mode.text": "文本", "toolbox.extract.textAppend": "提取文本", "toolbox.extract.title": "PDF提取", "toolbox.extract.unsupported": "暂不支持", "toolbox.extract.unsupportedMessage": "图片提取功能暂未开放", "toolbox.insert.description": "向原始PDF文件中插入空白页或指定文件", "toolbox.insert.fileNameAppend": "插入", "toolbox.insert.insertCount": "插入数量", "toolbox.insert.insertFile": "插入文件", "toolbox.insert.insertPage": "插入页码", "toolbox.insert.insertPosition": "插入位置", "toolbox.insert.insertType": "插入类型", "toolbox.insert.mode.blank": "插入空白页", "toolbox.insert.mode.file": "插入文件", "toolbox.insert.orientation": "纸张方向", "toolbox.insert.orientationOptions.landscape": "横向", "toolbox.insert.orientationOptions.portrait": "纵向", "toolbox.insert.paperSize": "纸张尺寸", "toolbox.insert.position.after_all": "所有页之后", "toolbox.insert.position.after_first": "第一页之后", "toolbox.insert.position.after_last": "最后一页之后", "toolbox.insert.position.after_page": "指定页码之后", "toolbox.insert.position.before_all": "所有页之前", "toolbox.insert.position.before_even": "偶数页之前", "toolbox.insert.position.before_first": "第一页之前", "toolbox.insert.position.before_last": "最后一页之前", "toolbox.insert.position.before_odd": "奇数页之前", "toolbox.insert.position.before_page": "指定页码之前", "toolbox.insert.title": "PDF插入", "toolbox.merge.atLeastTwoFilesRequired": "至少选择两个文件", "toolbox.merge.description": "支持将多个PDF文件合并为一个文件，或者将一个PDF文件多页合并为单页", "toolbox.merge.direction": "排序方向", "toolbox.merge.directionOptions.ascending": "升序", "toolbox.merge.directionOptions.descending": "降序", "toolbox.merge.fileNameAppend.multiFile": "等合并", "toolbox.merge.mergeType": "合并类型", "toolbox.merge.mode.file": "多文件合并", "toolbox.merge.mode.page": "多页合并", "toolbox.merge.page_merge_stem_append": "_页合并", "toolbox.merge.sortBy": "排序依据", "toolbox.merge.sortByOptions.createDate": "创建时间", "toolbox.merge.sortByOptions.modDate": "修改时间", "toolbox.merge.sortByOptions.name": "文件名顺序", "toolbox.merge.sortByOptions.numberPrefix": "文件名开头编号", "toolbox.merge.sortByOptions.numberSuffix": "文件名末尾编号", "toolbox.merge.sortByOptions.selection": "添加顺序", "toolbox.merge.title": "PDF合并", "toolbox.meta.author": "作者", "toolbox.meta.authorPlaceholder": "输入作者", "toolbox.meta.creationDate": "创建日期", "toolbox.meta.creationDatePlaceholder": "输入创建日期, 例如：2021-01-01 00:00:00", "toolbox.meta.creator": "创建者", "toolbox.meta.creatorPlaceholder": "输入创建者", "toolbox.meta.description": "设置PDF文件的元信息", "toolbox.meta.keywords": "关键词", "toolbox.meta.keywordsPlaceholder": "输入关键词", "toolbox.meta.modDate": "修改日期", "toolbox.meta.modDatePlaceholder": "输入修改日期, 例如：2021-01-01 00:00:00", "toolbox.meta.producer": "生产者", "toolbox.meta.producerPlaceholder": "输入生产者", "toolbox.meta.subject": "主题", "toolbox.meta.subjectPlaceholder": "输入主题", "toolbox.meta.title": "PDF元信息", "toolbox.meta.titlePlaceholder": "输入标题", "toolbox.meta.title_field": "标题", "toolbox.ocr.description": "支持批量OCR识别PDF文件", "toolbox.ocr.title": "PDF OCR", "toolbox.pageNumber.alignment": "对齐方式", "toolbox.pageNumber.customFont": "自定义字体", "toolbox.pageNumber.customStyle": "自定义样式", "toolbox.pageNumber.customStylePlaceholder": "输入自定义样式, %p表示当前页码，%P表示总页数, 例如: %p/%P", "toolbox.pageNumber.description": "为PDF文件添加页码。", "toolbox.pageNumber.enterFloatNumber": "请输入浮点数", "toolbox.pageNumber.fontColor": "字体颜色", "toolbox.pageNumber.fontFamily": "字体", "toolbox.pageNumber.fontFile": "字体文件", "toolbox.pageNumber.fontFilePlaceholder": "输入字体文件绝对路径，或拖拽文件到此处", "toolbox.pageNumber.fontSize": "字号", "toolbox.pageNumber.fontSizePlaceholder": "输入字号", "toolbox.pageNumber.fontSizeRequired": "请输入字号， 例如: 12", "toolbox.pageNumber.margin.bottom": "下边距", "toolbox.pageNumber.margin.bottomPlaceholder": "输入下边距", "toolbox.pageNumber.margin.bottomRequired": "请输入下边距", "toolbox.pageNumber.margin.invalid": "请输入大于等于0的数字", "toolbox.pageNumber.margin.left": "左边距", "toolbox.pageNumber.margin.leftPlaceholder": "输入左边距", "toolbox.pageNumber.margin.leftRequired": "请输入左边距", "toolbox.pageNumber.margin.right": "右边距", "toolbox.pageNumber.margin.rightPlaceholder": "输入右边距", "toolbox.pageNumber.margin.rightRequired": "请输入右边距", "toolbox.pageNumber.margin.top": "上边距", "toolbox.pageNumber.margin.topPlaceholder": "输入上边距", "toolbox.pageNumber.margin.topRequired": "请输入上边距", "toolbox.pageNumber.numberPosition": "页码位置", "toolbox.pageNumber.opacity": "不透明度", "toolbox.pageNumber.opacityPlaceholder": "输入不透明度，位于0-1之间", "toolbox.pageNumber.opacityRequired": "请输入不透明度， 例如: 0.5", "toolbox.pageNumber.selectAlignment": "选择对齐方式", "toolbox.pageNumber.selectFontFamily": "选择字体", "toolbox.pageNumber.selectNumberPosition": "选择页码位置", "toolbox.pageNumber.selectStyle": "选择样式", "toolbox.pageNumber.style": "页码样式", "toolbox.pageNumber.title": "PDF页码", "toolbox.pdf_ocr.is_merge_mode": "合并输出", "toolbox.pdf_ocr.is_show_page_sep": "显示页分隔符", "toolbox.permission.description": "恢复受保护PDF文件的权限", "toolbox.permission.title": "恢复权限", "toolbox.reorder.description": "重新排序PDF文件中的页面。", "toolbox.reorder.newPageOrder": "新页码顺序", "toolbox.reorder.newPageOrderPlaceholder": "输入新页码顺序, 例如前两页对调填写: 2,1,3-N", "toolbox.reorder.reorder": "页面重排", "toolbox.reorder.title": "PDF重排", "toolbox.replace.blankTab": "替换为空白页", "toolbox.replace.description": "替换PDF文件中的页面。", "toolbox.replace.fileTab": "替换为指定文件", "toolbox.replace.title": "PDF替换", "toolbox.rotate.description": "旋转PDF文件中的页面。", "toolbox.rotate.rotationAngle": "旋转角度", "toolbox.rotate.title": "PDF旋转", "toolbox.scale.description": "调整PDF页面大小。", "toolbox.scale.enterPositiveNumber": "请输入大于等于0的数字", "toolbox.scale.height": "高度", "toolbox.scale.heightPlaceholder": "输入页面高度(pt)", "toolbox.scale.heightRequired": "请输入高度", "toolbox.scale.paperSize": "纸张尺寸", "toolbox.scale.scaleRatio": "缩放比例", "toolbox.scale.scaleRatioPlaceholder": "输入缩放比例，例如：2", "toolbox.scale.scaleRatioRequired": "请输入缩放比例", "toolbox.scale.scaleType": "缩放类型", "toolbox.scale.scaleTypeOptions.custom": "自定义尺寸", "toolbox.scale.scaleTypeOptions.fixed": "固定尺寸", "toolbox.scale.scaleTypeOptions.ratio": "比例缩放", "toolbox.scale.selectPaperSize": "选择纸张尺寸", "toolbox.scale.stem_append": "_缩放", "toolbox.scale.title": "PDF缩放", "toolbox.scale.width": "宽度", "toolbox.scale.widthPlaceholder": "输入页面宽度(pt)", "toolbox.scale.widthRequired": "请输入宽度", "toolbox.split.bookmarkLevel": "目录级别", "toolbox.split.bookmarkSplit": "目录拆分", "toolbox.split.chunkSize": "分块大小", "toolbox.split.chunkSizeGreaterThanZero": "分块大小必须大于0", "toolbox.split.chunkSizePlaceholder": "输入分块大小", "toolbox.split.chunkSizeRequired": "请输入分块大小， 例如: 10", "toolbox.split.customSplit": "自定义拆分", "toolbox.split.customSplitSuffix": "自定义拆分", "toolbox.split.description": "支持将PDF文件拆分为多个PDF文件，支持均匀拆分或自定义拆分。", "toolbox.split.desktopOnly": "此功能仅限电脑端", "toolbox.split.enterInteger": "请输入整数", "toolbox.split.level1": "一级标题", "toolbox.split.level2": "二级标题", "toolbox.split.level3": "三级标题", "toolbox.split.pleaseSelectFile": "请选择文件", "toolbox.split.selectBookmarkLevel": "选择目录级别", "toolbox.split.splitSuffix": "拆分", "toolbox.split.splitType": "拆分类型", "toolbox.split.title": "PDF拆分", "toolbox.split.uniformSplit": "均匀拆分", "toolbox.split.uniformSplitSuffix": "均匀拆分", "toolbox.validation.enterFloat": "请输入浮点数", "toolbox.validation.enterInteger": "请输入整数", "toolbox.validation.enterNonNegativeNumber": "请输入大于等于0的数字", "toolbox.validation.mustBeGreaterThanZero": "必须大于0", "toolbox.watermark.addTab": "添加水印", "toolbox.watermark.advancedOptions": "高级选项", "toolbox.watermark.createWatermarkImageFailed": "创建水印图片失败", "toolbox.watermark.description": "支持PDF文件添加水印或去除水印", "toolbox.watermark.desktopOnlyFeature": "该功能仅限电脑端使用", "toolbox.watermark.detectWatermarkFailed": "检测水印失败", "toolbox.watermark.fileNotExist": "输入文件不存在", "toolbox.watermark.fontFamilyList.sourceHanSansSC": "思源黑体", "toolbox.watermark.image.file": "图片文件", "toolbox.watermark.image.opacity": "不透明度", "toolbox.watermark.image.position": "位置", "toolbox.watermark.image.rotation": "旋转角度", "toolbox.watermark.image.scale": "缩放比例", "toolbox.watermark.imageDataEmpty": "图片数据为空", "toolbox.watermark.invalidWatermarkIndex": "水印索引格式无效", "toolbox.watermark.limit_regin": "限定区域", "toolbox.watermark.lower_bounds": "阈值下限", "toolbox.watermark.lower_bounds_placeholder": "请填写阈值下限", "toolbox.watermark.lower_bounds_required": "请输入阈值下限", "toolbox.watermark.noValidRectangleAnnotation": "在指定页面上未找到有效的矩形注释", "toolbox.watermark.notSupport": "暂不支持", "toolbox.watermark.readImageDataFailed": "读取图片数据失败", "toolbox.watermark.removeTab": "去除水印", "toolbox.watermark.removeWatermarkFailed": "去除水印失败", "toolbox.watermark.savePdfFailed": "保存PDF失败", "toolbox.watermark.stepOptions.step1": "步骤一：识别水印索引", "toolbox.watermark.stepOptions.step2": "步骤二：正式去除水印", "toolbox.watermark.step_option": "步骤", "toolbox.watermark.text.content": "水印文本", "toolbox.watermark.text.customFont": "自定义字体", "toolbox.watermark.text.fontColor": "字体颜色", "toolbox.watermark.text.fontFamily": "字体", "toolbox.watermark.text.fontFile": "字体文件", "toolbox.watermark.text.fontSize": "字号", "toolbox.watermark.text.opacity": "不透明度", "toolbox.watermark.text.position": "位置", "toolbox.watermark.text.rotation": "旋转角度", "toolbox.watermark.text.selectFontFamily": "选择字体", "toolbox.watermark.text.target_color": "目标颜色", "toolbox.watermark.title": "PDF水印", "toolbox.watermark.type": "水印类型", "toolbox.watermark.typeOptions.image": "图片", "toolbox.watermark.typeOptions.text": "文本", "toolbox.watermark.upper_bounds": "阈值上限", "toolbox.watermark.upper_bounds_invalid": "请输入整数", "toolbox.watermark.upper_bounds_placeholder": "填写阈值上限", "toolbox.watermark.upper_bounds_range": "请输入0～255之间的整数", "toolbox.watermark.upper_bounds_required": "请填写阈值上限", "toolbox.watermark.watermarkImageNotExist": "水印图片路径不存在", "toolbox.watermark.watermarkImageRequired": "请选择水印图片", "toolbox.watermark.watermarkIndexRequired": "请输入水印索引", "toolbox.watermark.watermarkPageRequired": "请指定水印所在页码", "toolbox.watermark.watermarkRemoveTypeList.content": "内容水印", "toolbox.watermark.watermarkRemoveTypeList.edit_text": "(可编辑)文字水印", "toolbox.watermark.watermarkRemoveTypeList.image": "图片水印", "toolbox.watermark.watermarkRemoveTypeList.mask": "遮罩水印", "toolbox.watermark.watermarkRemoveTypeList.path": "路径水印", "toolbox.watermark.watermarkRemoveTypeList.pixel": "色阶去水印", "toolbox.watermark.watermarkRemoveTypeList.type": "类型水印", "toolbox.watermark.watermarkTextRequired": "请输入水印文本", "toolbox.watermark.wm_index": "水印索引", "toolbox.watermark.wm_index_placeholder": "输入水印索引", "toolbox.watermark.wm_page_number": "含水印页码", "toolbox.watermark.wm_page_number_invalid": "只需要输入一个页码，例如1", "toolbox.watermark.wm_page_number_placeholder": "输入含水印页码，如：1", "toolbox.watermark.wm_page_number_required": "请输入水印页码", "toolbox.watermark.wm_text_cannot_empty": "水印文本不能为空", "toolbox.watermark.wm_text_to_remove": "水印文本", "toolbox.watermark.wm_text_to_remove_placeholder": "输入待删除的水印文本", "userCenter.annually": "年会员", "userCenter.buyMembership": "购买会员", "userCenter.expiryDate": "到期时间", "userCenter.lifetime": "终身会员", "userCenter.memberInfo": "会员信息", "userCenter.memberType": "会员类型", "userCenter.monthly": "月会员", "userCenter.noMember": "无", "userCenter.title": "个人中心", "userCenter.unknown": "未知", "videoNotes.action.delete": "删除", "videoNotes.action.select": "选择", "videoNotes.cloze.freeGuess": "自由猜", "videoNotes.cloze.maskAllGuessAll": "遮全猜全", "videoNotes.cloze.maskAllGuessOne": "遮全猜一", "videoNotes.cloze.maskOneGuessOne": "遮一猜一", "videoNotes.cloze.scratchGuess": "刮刮乐", "videoNotes.debug.debugProcessError": "调试过程中发生错误: {error}", "videoNotes.debug.serverStatusInfo": "服务器运行: {serverRunning}, 连接数: {connections}", "videoNotes.debug.webSocketDebugComplete": "WebSocket调试完成", "videoNotes.debug.webSocketDebugFailed": "WebSocket调试失败", "videoNotes.defaults.deckName": "<PERSON>导入", "videoNotes.dialog.addBilibiliVideo": "添加B站视频", "videoNotes.dialog.addNetworkVideo": "添加网络视频", "videoNotes.dialog.cancel": "取消", "videoNotes.dialog.confirm": "确定", "videoNotes.dialog.selectRootFolder": "选择根文件夹", "videoNotes.dialog.selectStorageLocation": "选择媒体文件存储位置", "videoNotes.dialog.selectStorageLocationContent": "请选择一个文件夹来存储媒体文件", "videoNotes.dialog.videoTitle": "视频标题（可选）", "videoNotes.dialog.videoTitleHint": "请输入视频标题", "videoNotes.dialog.videoUrl": "视频URL", "videoNotes.dialog.videoUrlHint": "请输入视频URL", "videoNotes.empty.addVideoPrompt": "请先添加视频", "videoNotes.empty.playlistEmpty": "播放列表为空", "videoNotes.error.annotationFailed": "批注截图失败", "videoNotes.error.cannotConnectToBrowserPlugin": "无法连接到浏览器插件", "videoNotes.error.cannotSaveScreenshot": "无法保存截图: {error}", "videoNotes.error.cannotVerifyBrowserExtension": "无法验证浏览器扩展状态", "videoNotes.error.checkServerStartup": "请检查服务器是否正确启动", "videoNotes.error.clozeImageFailed": "图片挖空失败", "videoNotes.error.connectionCheckFailed": "连接检查失败", "videoNotes.error.customTemplateEmpty": "自定义格式模板不能为空", "videoNotes.error.deckNameEmpty": "牌组名称不能为空", "videoNotes.error.filePermissionRequired": "需要文件管理权限来读取文件", "videoNotes.error.ocrInitFailed": "OCR初始化失败", "videoNotes.error.operationFailed": "操作失败", "videoNotes.error.playPauseCommandFailed": "播放/暂停命令执行失败", "videoNotes.error.processVideoError": "处理视频文件时出错", "videoNotes.error.saveFailed": "保存失败", "videoNotes.error.screenshotFailed": "截图失败", "videoNotes.error.screenshotRequestFailed": "截图请求失败", "videoNotes.error.setRootDirectoryFirst": "请先设置根目录", "videoNotes.error.textExtractionFailed": "提取文字失败: {error}", "videoNotes.error.timestampRequestFailed": "时间戳请求失败", "videoNotes.error.title": "错误", "videoNotes.error.webSocketServerNotRunning": "WebSocket服务器未运行", "videoNotes.file.savePlaylist": "保存播放列表", "videoNotes.file.selectPlaylist": "选择播放列表文件", "videoNotes.linkFormat.custom": "自定义", "videoNotes.menu.openLocal": "打开本地视频", "videoNotes.menu.openNetwork": "打开网络视频", "videoNotes.menu.openPlaylist": "打开播放列表", "videoNotes.menu.preferences": "偏好设置", "videoNotes.message.annotationCopied": "复制批注截图成功", "videoNotes.message.settingsSaved": "设置已保存", "videoNotes.message.success": "成功", "videoNotes.message.videoAdded": "视频已添加到播放列表", "videoNotes.navigation.playlist": "播放列表", "videoNotes.navigation.subtitle": "字幕", "videoNotes.notification.addVideoFirst": "请先添加视频", "videoNotes.notification.annotationFailed": "批注截图失败", "videoNotes.notification.playerSettingsUpdated": "播放器设置已更新", "videoNotes.notification.playerSwitchedTo": "默认播放器已切换为: {player}", "videoNotes.notification.screenshotCopyFailed": "复制截图失败", "videoNotes.notification.screenshotCopySuccess": "复制截图成功", "videoNotes.notification.timestampCopyFailed": "复制时间戳链接失败", "videoNotes.notification.timestampCopySuccess": "复制时间戳链接成功", "videoNotes.player.browser": "浏览器", "videoNotes.player.builtin": "内置播放器", "videoNotes.playlist.empty": "播放列表为空", "videoNotes.playlist.loadError": "加载失败：{error}", "videoNotes.playlist.loadFailed": "无法加载播放列表文件", "videoNotes.playlist.loadSuccess": "播放列表加载成功", "videoNotes.playlist.loaded": "播放列表已加载", "videoNotes.playlist.saveFailed": "保存播放列表失败", "videoNotes.playlist.saved": "播放列表已保存", "videoNotes.progress.generatingTimestamp": "正在生成时间戳", "videoNotes.progress.gettingScreenshot": "正在获取截图", "videoNotes.progress.pleaseWait": "请稍候...", "videoNotes.settings.anki.defaultClozeMode": "默认挖空模式", "videoNotes.settings.anki.defaultClozeModeePlaceholder": "选择默认挖空模式", "videoNotes.settings.anki.defaultDeck": "默认牌组", "videoNotes.settings.anki.defaultDeckPlaceholder": "输入默认牌组名称", "videoNotes.settings.anki.defaultTags": "默认标签", "videoNotes.settings.anki.defaultTagsPlaceholder": "输入默认标签，用英文逗号分隔", "videoNotes.settings.anki.oneClozePeCard": "一空一卡", "videoNotes.settings.backlink.autoPaste": "自动粘贴", "videoNotes.settings.backlink.customTemplate": "自定义格式模板", "videoNotes.settings.backlink.customTemplatePlaceholder": "例如：[{time}]({url})", "videoNotes.settings.backlink.enablePathEncoding": "启用路径编码", "videoNotes.settings.backlink.linkFormat": "链接格式", "videoNotes.settings.backlink.linkFormatPlaceholder": "选择链接格式", "videoNotes.settings.backlink.pauseAfterCopyLink": "复制链接后暂停", "videoNotes.settings.backlink.pauseAfterScreenshot": "复制截图后暂停", "videoNotes.settings.backlink.rootFolder": "根文件夹", "videoNotes.settings.backlink.rootFolderPlaceholder": "请填写根文件夹路径", "videoNotes.settings.backlink.useRelativePath": "使用相对路径", "videoNotes.settings.category.ankiSettings": "<PERSON><PERSON>设置", "videoNotes.settings.category.backlinkSettings": "回链设置", "videoNotes.settings.category.playbackControl": "播放控制", "videoNotes.settings.category.shortcutSettings": "快捷键设置", "videoNotes.settings.player.brightnessGesture": "手势调节亮度", "videoNotes.settings.player.defaultPlayer": "默认播放器", "videoNotes.settings.player.defaultSpeedValue": "默认倍速值", "videoNotes.settings.player.doubleTapPause": "双击暂停", "videoNotes.settings.player.doubleTapSeek": "双击快进/快退", "videoNotes.settings.player.longPressSpeed": "长按倍速", "videoNotes.settings.player.seekSeconds": "快进/快退秒数", "videoNotes.settings.player.showMiniProgress": "显示迷你进度条", "videoNotes.settings.player.volumeGesture": "手势调节音量", "videoNotes.settings.shortcuts.annotScreenshot": "批注截图", "videoNotes.settings.shortcuts.backward15": "快退15秒", "videoNotes.settings.shortcuts.backward5": "快退5秒", "videoNotes.settings.shortcuts.copyScreenshot": "复制截图", "videoNotes.settings.shortcuts.copyTimestamp": "复制时间戳", "videoNotes.settings.shortcuts.disableAll": "全部禁用", "videoNotes.settings.shortcuts.forward15": "快进15秒", "videoNotes.settings.shortcuts.forward5": "快进5秒", "videoNotes.settings.shortcuts.playPause": "播放/暂停", "videoNotes.shortcuts.allDisabled": "已禁用全部快捷键", "videoNotes.shortcuts.allEnabled": "已启用全部快捷键", "videoNotes.status.notSet": "未设置", "videoNotes.title.main": "视频笔记", "videoNotes.title.settings": "视频笔记设置", "videoNotes.tooltip.clearPlaylist": "清空播放列表", "videoNotes.tooltip.openLocal": "打开本地视频", "videoNotes.tooltip.openNetwork": "打开网络视频", "videoNotes.tooltip.openPlaylist": "打开播放列表", "videoNotes.tooltip.savePlaylist": "保存播放列表", "videoNotes.validation.directoryNotExists": "目录路径不存在", "videoNotes.validation.rootFolderEmpty": "根文件夹不能为空", "videoNotes.validation.selectDirectory": "请选择目录", "videoNotes.validation.selectDirectoryNotFile": "请选择目录, 不是文件", "watermark.completion.detectionCompleted": "内容水印检测完成", "watermark.completion.imageWatermarkDetectionCompleted": "图片水印检测完成", "watermark.completion.pathWatermarkDetectionCompleted": "路径水印检测完成", "watermark.completion.pdfToImageCompleted": "像素水印检测完成", "watermark.default.watermarkText": "水印", "watermark.error.addImageWatermarkFailed": "添加图片水印失败", "watermark.error.addTextWatermarkFailed": "添加文本水印失败", "watermark.error.contentWatermarkDetectionFailed": "内容水印检测失败", "watermark.error.imageWatermarkDetectionFailed": "图片水印检测失败", "watermark.filename.watermarkAdded": "_watermark_added", "watermark.filename.watermarkIndexDetected": "_识别水印索引", "watermark.filename.watermarkRemoved": "_去水印版", "watermark.log.font": "字体", "watermark.log.fontSize": "字体大小", "watermark.log.watermarkText": "水印文本", "watermark.progress.convertingPdfToImage": "转换PDF为图片进行像素分析", "watermark.progress.detectingContentWatermark": "检测内容水印", "watermark.progress.detectingImageWatermark": "检测图片水印", "watermark.progress.detectingPathWatermark": "检测路径水印", "watermark.progress.processingContentWatermark": "处理内容水印", "watermark.progress.processingEditableTextWatermark": "处理可编辑文字水印", "watermark.progress.processingImageWatermark": "处理图片水印", "watermark.progress.processingPathWatermark": "处理路径水印", "watermark.progress.processingPixelWatermark": "处理像素水印"}